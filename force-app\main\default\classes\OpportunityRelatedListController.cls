/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 08-22-2025
 * @last modified by  : <EMAIL>
**/
public with sharing class OpportunityRelatedListController {

    @AuraEnabled
    public static List<Opportunity> getRelatedOpportunities(Id accountId, List<String> stageNames) {

            List<Opportunity> oppList = [SELECT Id, Name, AreasOfNeedFormula__c, AssignedTo__c, AssignedTo__r.Name, CreatedDate, TemperatureFormula__c, Product__c, Probability, Amount, ClosingDate__c, StageName, Channel__c,DueDateFormula__c,Salespoint__c,SalespointName__c, PolicyChannel__c, CIP__c, Tipologia_soggetto_Unipol__c, JourneyStep__c, HasCallMeBackFormula__c, AccountHyperlink__c
                                    FROM Opportunity
                                    WHERE AccountId = :accountId AND StageName IN :stageNames AND RecordType.DeveloperName != 'Prodotto'
                                    ORDER BY WorkingSLAExpiryDate__c DESC];
    
        return oppList;
    }

    //RP 22/08/2025 Per il contact Center, visualizzare tutte le trattative
    @AuraEnabled
    public static List<Opportunity> getRelatedOpportunitiesCC(Id accountId, Id caseId) {
        List<Opportunity> oppList = new List<Opportunity>();
        List<Case> caseList = [SELECT Id, Opportunity__c FROM Case WHERE Id = :caseId LIMIT 1];

            oppList = [SELECT Id, Name, AreasOfNeedFormula__c, AssignedTo__c, AssignedTo__r.Name, CreatedDate, TemperatureFormula__c, Product__c, Probability, Amount, ClosingDate__c, StageName, Channel__c,DueDateFormula__c,Salespoint__c,SalespointName__c, PolicyChannel__c, CIP__c, Tipologia_soggetto_Unipol__c, JourneyStep__c, HasCallMeBackFormula__c, AccountHyperlink__c
                                    FROM Opportunity
                                    WHERE AccountId = :accountId AND Id !=: caseList[0].Opportunity__c AND RecordType.DeveloperName != 'Prodotto' // Valutare aggiunta di altre condizioni
                                    ORDER BY WorkingSLAExpiryDate__c DESC];
        
    
        return oppList;
    }

    @AuraEnabled
    public static Account getAccountInfo(String accountId){
        
        return [SELECT Id, Name FROM Account WHERE Id = :accountId LIMIT 1];
    }

    @AuraEnabled
    public static User getUserInfo(String userId){
        try {
            
            return [SELECT Id, Name, IdAzienda__c FROM User WHERE Id = :userId LIMIT 1];

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}