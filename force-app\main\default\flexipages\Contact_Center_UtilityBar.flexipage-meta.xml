<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>runtime_service_omnichannel:omniWidget</componentName>
                <identifier>runtime_service_omnichannel_omniWidget</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>eager</name>
                    <type>decorator</type>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <type>decorator</type>
                    <value>480</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>icon</name>
                    <type>decorator</type>
                    <value>call</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <type>decorator</type>
                    <value>Phone</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>scrollable</name>
                    <type>decorator</type>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>width</name>
                    <type>decorator</type>
                    <value>340</value>
                </componentInstanceProperties>
                <componentName>opencti:softPhone</componentName>
                <identifier>opencti_softPhone</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>macros:macroUtilityItem</componentName>
                <identifier>macros_macroUtilityItem</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>console:history</componentName>
                <identifier>console_history</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>notes:utilityBarNoteList</componentName>
                <identifier>notes_utilityBarNoteList</identifier>
            </componentInstance>
        </itemInstances>
        <name>utilityItems</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>backgroundComponents</name>
        <type>Background</type>
    </flexiPageRegions>
    <masterLabel>Contact Center UtilityBar</masterLabel>
    <template>
        <name>one:utilityBarTemplateDesktop</name>
        <properties>
            <name>isLeftAligned</name>
            <value>true</value>
        </properties>
    </template>
    <type>UtilityBar</type>
</FlexiPage>
