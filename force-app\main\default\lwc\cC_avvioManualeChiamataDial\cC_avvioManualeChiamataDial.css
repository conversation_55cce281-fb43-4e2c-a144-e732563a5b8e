/* .wrapper { min-height: 96px; }
.phone-row { display: grid; grid-template-columns: 120px 1fr; grid-gap: .75rem; align-items: center; }
.label { color: var(--lwc-colorTextWeak, #5e5e5e); }
.value { font-weight: 600; }
.empty { color: var(--lwc-colorTextWeak, #5e5e5e); } */

.call-card {
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    background: #ffffff;
}

.call-header {
    background-color: #e8f5e9; /* verde chiaro come nello screenshot */
    border-bottom: 1px solid #d8dde6;
}

.call-header .icon {
    fill: #2e844a;
    margin-right: 0.5rem;
}

.call-header .title {
    font-weight: 600;
    color: #2e844a;
    font-size: 1rem;
}

.phone {
    font-size: 0.95rem;
    font-weight: 600;
    color: #0070d2;
}