/* Effetto hover sulla riga della datatable */
.lightning-datatable tbody tr:hover {
    background-color: #f4f6f9;
    cursor: pointer;
}

/* Box dei dettagli (espansione riga o sezione sotto la tabella) */
.slds-box {
    border-radius: 0.5rem;
    border: 1px solid #d8dde6;
    background: #fafafa;
}

/* Margine fra tabella e dettagli */
.slds-m-top_small {
    margin-top: 0.75rem;
}

/* Hover sulle righe */
lightning-datatable tbody tr:hover {
    background-color: #f4f6f9;
}

/* Effetto hover sulla riga della datatable */
lightning-datatable tbody tr:hover {
    background-color: #f4f6f9;
    cursor: pointer;
}

/* Box dei dettagli (espansione riga) */
.detail-box {
    border-radius: 0.5rem;
    border: 1px solid #d8dde6;
    background: #fafafa;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}

/* Header: rendi il titolo più marcato */
.slds-card__header-title span {
    font-weight: 600;
}