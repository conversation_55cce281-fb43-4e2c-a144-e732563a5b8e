import CcDisambiguationCoreChild from "c/ccDisambiguationCoreChild";

export default class CcDisambiguationEffectiveDate extends CcDisambiguationCoreChild {
  get overrideData() {
    return {
      ...this.formData,
      effectiveDate: {
        ...this.formData.effectiveDate,
        validation: (event) => {
          const value = event.target.value;
          if (!value) {
            event.target.setCustomValidity("Data Decorrenza non può essere vuota.");
            return;
          }

          const today = new Date();
          today.setHours(0, 0, 0, 0);

          // const inputDate = new Date(value + "T00:00:00");
          const inputDate = new Date(value);
          today.setHours(0, 0, 0, 0);

          if (inputDate < today) {
            event.target.setCustomValidity(
              "La data effetto non può essere precedente alla data odierna"
            );
          } else {
            event.target.setCustomValidity("");
            this.handleChange(event);
          }
        },
      },
    };
  }
}
