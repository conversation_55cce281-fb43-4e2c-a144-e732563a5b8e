import { LightningElement, api, wire } from 'lwc';
import { openSubtab, EnclosingTabId, getTabInfo } from 'lightning/platformWorkspaceApi';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';

import RELATED_RECORD_ID from '@salesforce/schema/VoiceCall.RelatedRecordId';

const FIELDS = [RELATED_RECORD_ID];

export default class ccOpenRelatedSubTab extends LightningElement {
    @api recordId;

    @wire(EnclosingTabId) enclosingTabId;


    @wire(getRecord, { recordId: '$recordId', fields: FIELDS })
    async wiredRecord({ data, error }) {
        if (!data) return;

        const relatedId = getFieldValue(data, RELATED_RECORD_ID);
        if (!relatedId) {
            return;
        }

        const tabId = this.enclosingTabId;
        if (!tabId){
            return;
        } 

        const tabInfo = await getTabInfo(tabId);
        const primaryTabId = tabInfo.isSubtab ? tabInfo.parentTabId : tabInfo.tabId;

        await openSubtab(primaryTabId, { recordId: relatedId, focus: false });
    }
}