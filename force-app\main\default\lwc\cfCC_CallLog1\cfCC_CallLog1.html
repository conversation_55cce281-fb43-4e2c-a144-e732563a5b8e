<template>
            <div class="slds-grid slds-wrap slds-is-relative ">
            <div style="position: fixed;z-index: 999999;top: 0;right: 0;">
  <omnistudio-toast class="flexcard-toast-element" theme="slds" title="" message="" styletype=""> </omnistudio-toast>
</div>
            <div if:true={showLoader} class="slds-is-absolute vloc-loader_override" style="height: 100%; width: 100%; min-height:50px; background: transparent; z-index: 99;">
  <div>
   <omnistudio-spinner
      variant="brand"
      alternative-text="Loading content..."
      size="medium"
      theme="slds"
      ></omnistudio-spinner>
  </div>
</div>
            <template if:false={hasPermission}>
              You don't have required permissions to view this card.
            </template>
            <template if:true={hasPermission}>
              <template if:true={hasRecords}>
                        <template for:each={_records} for:item="record" for:index="rindex">
                            <omnistudio-flex-card-state if:true={record}  key={record._flex.uniqueKey} data-recordid={record.Id} record={record} data-statue="true"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state0element0" class="slds-col   slds-p-around_x-small  slds-size_12-of-12  " data-rindex={rindex} style="background-color:#DDDDDD;      
         " >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0" class="slds-col   slds-text-align_center slds-p-around_x-small  slds-size_2-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state0element0block_element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element0block_element0block_element0_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="standard:record"  size="medium"  extraclass="slds-icon_container slds-icon-standard-record "  variant="inverse"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state0element0block_element1" class="slds-col   slds-size_10-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element1_child"   card={card}  record={record}  merge-field="%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state0element1" class="slds-col   slds-p-around_medium  slds-size_6-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state0element1_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element1block_element0" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><c-cf-c-c_-call-log-child data-style-id="state0element1block_element0_child"  tracking-obj={_childCardTrackingObj}  is-inside-parent={insideParent} parent-uniquekey={uniqueKey}  card-node="\{record.voiceCalls[0]}"  parent-record={record}  records={_records}  parent-mergefields={card}  record-id={recordId}  object-api-name={objectApiName}  theme="slds"   onupdateparent={updateParentData}></c-cf-c-c_-call-log-child>
    </div>
        </div></omnistudio-block>
    </div><div data-style-id="state0element2" class="slds-col   slds-p-around_medium  slds-size_6-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state0element2_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element2block_element0" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><c-cf-c-c_-call-log-child data-style-id="state0element2block_element0_child"  tracking-obj={_childCardTrackingObj}  is-inside-parent={insideParent} parent-uniquekey={uniqueKey}  card-node="\{record.voiceCalls[1]}"  parent-record={record}  records={_records}  parent-mergefields={card}  record-id={recordId}  object-api-name={objectApiName}  theme="slds"   onupdateparent={updateParentData}></c-cf-c-c_-call-log-child>
    </div>
        </div></omnistudio-block>
    </div><div data-style-id="state0element3" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-flex-action data-style-id="state0element3_child"  tracking-obj={_childCardTrackingObj}  class= "state0element3_child flexActionElement" 
        data-action-element-class="state0element3_child" data-action-key="state0element3" onexecuteaction={performAction} data-show-spinner="false"
        data-element-label="action3"   state-obj={record}  card={card}  record={record}  label="Visualizza tutto"  icon-name="standard-default"  action-list='\[{"stateAction":{"id":"flex-action-1755510183039","type":"Custom","targetType":"Record Relationship","openUrlIn":"Current Window","Record Relationship":{"targetName":"VoiceCall","targetAction":"view","targetId":"{recordId}","targetRelationship":"RelatedRecords"}},"key":"1755510106778-tp76e38f2","label":"Action","draggable":true,"isOpen":true,"actionIndex":0}]'  hide-action-icon="true"  flyout-details='\{}'  styles='\{"label":{"textAlign":"center"}}'  theme="slds"  ></omnistudio-flex-action>
      </div>
        </div>
      </omnistudio-flex-card-state><omnistudio-flex-card-state if:true={record}  key={record._flex.uniqueKey} data-recordid={record.Id} record={record} data-statue="true"  data-index="1" data-rindex={rindex} class="cf-vlocity-state-1 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state1element0" class="slds-col   slds-p-around_x-small  slds-size_12-of-12  " data-rindex={rindex} style="background-color:#DDDDDD;      
         " >
    <omnistudio-block data-style-id="state1element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state1element0block_element0" class="slds-col   slds-text-align_center slds-p-around_x-small  slds-size_2-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state1element0block_element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state1element0block_element0block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state1element0block_element0block_element0_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="standard:record"  size="medium"  extraclass="slds-icon_container slds-icon-standard-record "  variant="inverse"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state1element0block_element1" class="slds-col   slds-size_10-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state1element0block_element1_child"   card={card}  record={record}  merge-field="%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state1element1" class="slds-col   slds-p-around_medium  slds-size_12-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state1element1_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state1element1block_element0" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><c-cf-c-c_-call-log-child data-style-id="state1element1block_element0_child"  tracking-obj={_childCardTrackingObj}  is-inside-parent={insideParent} parent-uniquekey={uniqueKey}  card-node="\{record.voiceCalls[0]}"  parent-record={record}  records={_records}  parent-mergefields={card}  record-id={recordId}  object-api-name={objectApiName}  theme="slds"   onupdateparent={updateParentData}></c-cf-c-c_-call-log-child>
    </div>
        </div></omnistudio-block>
    </div><div data-style-id="state1element2" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-flex-action data-style-id="state1element2_child"  tracking-obj={_childCardTrackingObj}  class= "state1element2_child flexActionElement" 
        data-action-element-class="state1element2_child" data-action-key="state1element2" onexecuteaction={performAction} data-show-spinner="false"
        data-element-label="action3"   state-obj={record}  card={card}  record={record}  label="Visualizza tutto"  icon-name="standard-default"  action-list='\[{"stateAction":{"id":"flex-action-1755510183039","type":"Custom","targetType":"Record Relationship","openUrlIn":"Current Window","Record Relationship":{"targetName":"VoiceCall","targetAction":"view","targetId":"{recordId}","targetRelationship":"RelatedRecords"}},"key":"1755510106778-tp76e38f2","label":"Action","draggable":true,"isOpen":true,"actionIndex":0}]'  hide-action-icon="true"  flyout-details='\{}'  styles='\{"label":{"textAlign":"center"}}'  theme="slds"  ></omnistudio-flex-action>
      </div>
        </div>
      </omnistudio-flex-card-state><omnistudio-flex-card-state if:true={record}  key={record._flex.uniqueKey} data-recordid={record.Id} record={record} data-statue="true"  data-index="2" data-rindex={rindex} class="cf-vlocity-state-2 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state2element0" class="slds-col   slds-p-around_x-small  slds-size_12-of-12  " data-rindex={rindex} style="background-color:#DDDDDD;      
         " >
    <omnistudio-block data-style-id="state2element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state2element0block_element0" class="slds-col   slds-text-align_center slds-p-around_x-small  slds-size_2-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state2element0block_element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state2element0block_element0block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state2element0block_element0block_element0_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="standard:record"  size="medium"  extraclass="slds-icon_container slds-icon-standard-record "  variant="inverse"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state2element0block_element1" class="slds-col   slds-size_10-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state2element0block_element1_child"   card={card}  record={record}  merge-field="%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                        </template>
                      </template>
                      <template if:false={hasRecords}>
                            <omnistudio-flex-card-state  record={record} data-statue="false"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state0element0" class="slds-col   slds-p-around_x-small  slds-size_12-of-12  " data-rindex={rindex} style="background-color:#DDDDDD;      
         " >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0" class="slds-col   slds-text-align_center slds-p-around_x-small  slds-size_2-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state0element0block_element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element0block_element0block_element0_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="standard:record"  size="medium"  extraclass="slds-icon_container slds-icon-standard-record "  variant="inverse"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state0element0block_element1" class="slds-col   slds-size_10-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element1_child"   card={card}  record={record}  merge-field="%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state0element1" class="slds-col   slds-p-around_medium  slds-size_6-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state0element1_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element1block_element0" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><c-cf-c-c_-call-log-child data-style-id="state0element1block_element0_child"  tracking-obj={_childCardTrackingObj}  is-inside-parent={insideParent} parent-uniquekey={uniqueKey}  card-node="\{record.voiceCalls[0]}"  parent-record={record}  records={_records}  parent-mergefields={card}  record-id={recordId}  object-api-name={objectApiName}  theme="slds"   onupdateparent={updateParentData}></c-cf-c-c_-call-log-child>
    </div>
        </div></omnistudio-block>
    </div><div data-style-id="state0element2" class="slds-col   slds-p-around_medium  slds-size_6-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state0element2_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element2block_element0" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><c-cf-c-c_-call-log-child data-style-id="state0element2block_element0_child"  tracking-obj={_childCardTrackingObj}  is-inside-parent={insideParent} parent-uniquekey={uniqueKey}  card-node="\{record.voiceCalls[1]}"  parent-record={record}  records={_records}  parent-mergefields={card}  record-id={recordId}  object-api-name={objectApiName}  theme="slds"   onupdateparent={updateParentData}></c-cf-c-c_-call-log-child>
    </div>
        </div></omnistudio-block>
    </div><div data-style-id="state0element3" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-flex-action data-style-id="state0element3_child"  tracking-obj={_childCardTrackingObj}  class= "state0element3_child flexActionElement" 
        data-action-element-class="state0element3_child" data-action-key="state0element3" onexecuteaction={performAction} data-show-spinner="false"
        data-element-label="action3"   state-obj={record}  card={card}  record={record}  label="Visualizza tutto"  icon-name="standard-default"  action-list='\[{"stateAction":{"id":"flex-action-1755510183039","type":"Custom","targetType":"Record Relationship","openUrlIn":"Current Window","Record Relationship":{"targetName":"VoiceCall","targetAction":"view","targetId":"{recordId}","targetRelationship":"RelatedRecords"}},"key":"1755510106778-tp76e38f2","label":"Action","draggable":true,"isOpen":true,"actionIndex":0}]'  hide-action-icon="true"  flyout-details='\{}'  styles='\{"label":{"textAlign":"center"}}'  theme="slds"  ></omnistudio-flex-action>
      </div>
        </div>
      </omnistudio-flex-card-state><omnistudio-flex-card-state  record={record} data-statue="false"  data-index="1" data-rindex={rindex} class="cf-vlocity-state-1 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state1element0" class="slds-col   slds-p-around_x-small  slds-size_12-of-12  " data-rindex={rindex} style="background-color:#DDDDDD;      
         " >
    <omnistudio-block data-style-id="state1element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state1element0block_element0" class="slds-col   slds-text-align_center slds-p-around_x-small  slds-size_2-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state1element0block_element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state1element0block_element0block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state1element0block_element0block_element0_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="standard:record"  size="medium"  extraclass="slds-icon_container slds-icon-standard-record "  variant="inverse"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state1element0block_element1" class="slds-col   slds-size_10-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state1element0block_element1_child"   card={card}  record={record}  merge-field="%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state1element1" class="slds-col   slds-p-around_medium  slds-size_12-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state1element1_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state1element1block_element0" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><c-cf-c-c_-call-log-child data-style-id="state1element1block_element0_child"  tracking-obj={_childCardTrackingObj}  is-inside-parent={insideParent} parent-uniquekey={uniqueKey}  card-node="\{record.voiceCalls[0]}"  parent-record={record}  records={_records}  parent-mergefields={card}  record-id={recordId}  object-api-name={objectApiName}  theme="slds"   onupdateparent={updateParentData}></c-cf-c-c_-call-log-child>
    </div>
        </div></omnistudio-block>
    </div><div data-style-id="state1element2" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-flex-action data-style-id="state1element2_child"  tracking-obj={_childCardTrackingObj}  class= "state1element2_child flexActionElement" 
        data-action-element-class="state1element2_child" data-action-key="state1element2" onexecuteaction={performAction} data-show-spinner="false"
        data-element-label="action3"   state-obj={record}  card={card}  record={record}  label="Visualizza tutto"  icon-name="standard-default"  action-list='\[{"stateAction":{"id":"flex-action-1755510183039","type":"Custom","targetType":"Record Relationship","openUrlIn":"Current Window","Record Relationship":{"targetName":"VoiceCall","targetAction":"view","targetId":"{recordId}","targetRelationship":"RelatedRecords"}},"key":"1755510106778-tp76e38f2","label":"Action","draggable":true,"isOpen":true,"actionIndex":0}]'  hide-action-icon="true"  flyout-details='\{}'  styles='\{"label":{"textAlign":"center"}}'  theme="slds"  ></omnistudio-flex-action>
      </div>
        </div>
      </omnistudio-flex-card-state><omnistudio-flex-card-state  record={record} data-statue="false"  data-index="2" data-rindex={rindex} class="cf-vlocity-state-2 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state2element0" class="slds-col   slds-p-around_x-small  slds-size_12-of-12  " data-rindex={rindex} style="background-color:#DDDDDD;      
         " >
    <omnistudio-block data-style-id="state2element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state2element0block_element0" class="slds-col   slds-text-align_center slds-p-around_x-small  slds-size_2-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state2element0block_element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state2element0block_element0block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state2element0block_element0block_element0_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="standard:record"  size="medium"  extraclass="slds-icon_container slds-icon-standard-record "  variant="inverse"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div>
        </div></omnistudio-block>
    </div><div data-style-id="state2element0block_element1" class="slds-col   slds-size_10-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state2element0block_element1_child"   card={card}  record={record}  merge-field="%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                      </template>
            </template>
            <template if:true={hasError}>
            {error}
            </template>
            
      <omnistudio-action action-wrapperclass="slds-hide" re-render-flyout class="action-trigger slds-col" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
      
      </omnistudio-action>
  
            <omnistudio-action class="execute-action" re-render-flyout action-wrapperclass="slds-hide" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
            
            </omnistudio-action>
            </div>
          </template>