let definition =
      {"states":[{"fields":[],"conditions":{"id":"state-condition-object","isParent":true,"group":[{"id":"state-new-condition-0","field":"listSize","operator":">","value":"1","type":"custom","hasMergeField":false}]},"definedActions":{"actions":[]},"name":"Active","isSmartAction":false,"smartAction":{},"styleObject":{"padding":[{"type":"around","size":"x-small"}],"margin":[{"type":"bottom","size":"x-small"}],"container":{"class":"slds-card"},"size":{"isResponsive":false,"default":"12"},"sizeClass":"slds-size_12-of-12","class":"slds-card slds-p-around_x-small slds-m-bottom_x-small"},"components":{"layer-0":{"children":[{"name":"Block","element":"block","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"size":{"isResponsive":false,"default":"12"},"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-p-around_x-small ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"#DDDDDD","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"background-color:#DDDDDD;      \n         "},"children":[{"key":"element_element_block_0_0_block_0_0","name":"Block","element":"block","size":{"isResponsive":false,"default":"2"},"stateIndex":0,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-text-align_center slds-p-around_x-small ","sizeClass":"slds-size_2-of-12 ","size":{"isResponsive":false,"default":"2"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"center","color":""},"inlineStyle":"","style":"      \n         "},"children":[{"key":"element_element_element_block_0_0_block_0_0_flexIcon_0_0","name":"Icon","element":"flexIcon","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","card":"{card}","iconType":"Salesforce SVG","iconName":"standard:record","size":"medium","extraclass":"slds-icon_container slds-icon-standard-record ","variant":"inverse","imgsrc":""},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12 ","size":{"isResponsive":false,"default":"12"}},"parentElementKey":"element_element_block_0_0_block_0_0","elementLabel":"Block-1-Block-0-Icon-0"}],"parentElementKey":"element_block_0_0","elementLabel":"Block-1-Block-0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-text-align_center slds-p-around_x-small ","sizeClass":"slds-size_2-of-12 ","size":{"isResponsive":false,"default":"2"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"center","color":""},"inlineStyle":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Text","element":"outputField","size":{"isResponsive":false,"default":"10"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_10-of-12 ","size":{"isResponsive":false,"default":"10"}},"elementLabel":"Block-1-Block-1-Text-0","key":"element_element_block_0_0_outputField_1_0","parentElementKey":"element_block_0_0"}],"elementLabel":"Block-0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"size":{"isResponsive":false,"default":"12"},"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-p-around_x-small ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"#DDDDDD","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"background-color:#DDDDDD;      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Block","element":"block","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"padding":[{"type":"around","size":"medium","label":"around:medium"}],"class":"slds-p-around_medium ","sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"children":[{"key":"element_element_block_1_0_childCardPreview_0_0","name":"FlexCard","element":"childCardPreview","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"cardName":"CC_CallLogChild","recordId":"{recordId}","cardNode":"{record.voiceCalls[0]}"},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12"},"parentElementKey":"element_block_1_0","elementLabel":"Block-1-FlexCard-0"}],"elementLabel":"Block-1","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[{"type":"around","size":"medium","label":"around:medium"}],"class":"slds-p-around_medium ","sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Block","element":"block","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"padding":[{"type":"around","size":"medium","label":"around:medium"}],"class":"slds-p-around_medium ","sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"children":[{"key":"element_element_block_2_0_childCardPreview_0_0","name":"FlexCard","element":"childCardPreview","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"cardName":"CC_CallLogChild","recordId":"{recordId}","cardNode":"{record.voiceCalls[1]}"},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12"},"parentElementKey":"element_block_2_0","elementLabel":"Block-1-FlexCard-0"}],"elementLabel":"Block-2","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[{"type":"around","size":"medium","label":"around:medium"}],"class":"slds-p-around_medium ","sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Action","element":"action","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"label":"Visualizza tutto","iconName":"standard-default","record":"{record}","card":"{card}","stateObj":"{record}","actionList":[{"stateAction":{"id":"flex-action-1755510183039","type":"Custom","targetType":"Record Relationship","openUrlIn":"Current Window","Record Relationship":{"targetName":"VoiceCall","targetAction":"view","targetId":"{recordId}","targetRelationship":"RelatedRecords"}},"key":"1755510106778-tp76e38f2","label":"Action","draggable":true,"isOpen":true,"actionIndex":0}],"showSpinner":"false","hideActionIcon":true,"flyoutDetails":{},"styles":{"label":{"textAlign":"center"}}},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12","padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":false,"default":"12"},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{"styles":{"label":{"textAlign":"center"}}},"text":{"align":"","color":""},"inlineStyle":""},"elementLabel":"Action-3","styleObjects":[{"key":0,"conditions":"default","styleObject":{"sizeClass":"slds-size_12-of-12","padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":false,"default":"12"},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{"styles":{"label":{"textAlign":"center"}}},"text":{"align":"","color":""},"inlineStyle":""},"label":"Default","name":"Default","conditionString":"","draggable":false}]}]}},"childCards":["CC_CallLogChild","CC_CallLogChild"],"actions":[],"omniscripts":[],"documents":[]},{"fields":[],"conditions":{"id":"state-condition-object","isParent":true,"group":[{"id":"state-new-condition-0","field":"listSize","operator":"==","value":"1","type":"custom","hasMergeField":false}]},"definedActions":{"actions":[]},"name":"Active one record","isSmartAction":false,"smartAction":{},"styleObject":{"padding":[{"type":"around","size":"x-small"}],"margin":[{"type":"bottom","size":"x-small"}],"container":{"class":"slds-card"},"size":{"isResponsive":false,"default":"12"},"sizeClass":"slds-size_12-of-12","class":"slds-card slds-p-around_x-small slds-m-bottom_x-small"},"components":{"layer-0":{"children":[{"name":"Block","element":"block","size":{"isResponsive":false,"default":"12"},"stateIndex":1,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"size":{"isResponsive":false,"default":"12"},"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-p-around_x-small ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"#DDDDDD","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"background-color:#DDDDDD;      \n         "},"children":[{"key":"element_element_block_0_0_block_0_0","name":"Block","element":"block","size":{"isResponsive":false,"default":"2"},"stateIndex":0,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-text-align_center slds-p-around_x-small ","sizeClass":"slds-size_2-of-12 ","size":{"isResponsive":false,"default":"2"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"center","color":""},"inlineStyle":"","style":"      \n         "},"children":[{"key":"element_element_element_block_0_0_block_0_0_flexIcon_0_0","name":"Icon","element":"flexIcon","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","card":"{card}","iconType":"Salesforce SVG","iconName":"standard:record","size":"medium","extraclass":"slds-icon_container slds-icon-standard-record ","variant":"inverse","imgsrc":""},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12 ","size":{"isResponsive":false,"default":"12"}},"parentElementKey":"element_element_block_0_0_block_0_0","elementLabel":"Block-1-Block-0-Icon-0"}],"parentElementKey":"element_block_0_0","elementLabel":"Block-1-Block-0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-text-align_center slds-p-around_x-small ","sizeClass":"slds-size_2-of-12 ","size":{"isResponsive":false,"default":"2"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"center","color":""},"inlineStyle":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Text","element":"outputField","size":{"isResponsive":false,"default":"10"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_10-of-12 ","size":{"isResponsive":false,"default":"10"}},"elementLabel":"Block-1-Block-1-Text-0","key":"element_element_block_0_0_outputField_1_0","parentElementKey":"element_block_0_0"}],"elementLabel":"Block-0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"size":{"isResponsive":false,"default":"12"},"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-p-around_x-small ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"#DDDDDD","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"background-color:#DDDDDD;      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Block","element":"block","size":{"isResponsive":false,"default":"12"},"stateIndex":1,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"padding":[{"type":"around","size":"medium","label":"around:medium"}],"class":"slds-p-around_medium ","sizeClass":"slds-size_12-of-12 ","size":{"isResponsive":false,"default":"12"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"children":[{"key":"element_element_block_1_0_childCardPreview_0_0","name":"FlexCard","element":"childCardPreview","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"cardName":"CC_CallLogChild","recordId":"{recordId}","cardNode":"{record.voiceCalls[0]}"},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12"},"parentElementKey":"element_block_1_0","elementLabel":"Block-1-FlexCard-0"}],"elementLabel":"Block-1","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[{"type":"around","size":"medium","label":"around:medium"}],"class":"slds-p-around_medium ","sizeClass":"slds-size_12-of-12 ","size":{"isResponsive":false,"default":"12"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Action","element":"action","size":{"isResponsive":false,"default":"12"},"stateIndex":1,"class":"slds-col ","property":{"label":"Visualizza tutto","iconName":"standard-default","record":"{record}","card":"{card}","stateObj":"{record}","actionList":[{"stateAction":{"id":"flex-action-1755510183039","type":"Custom","targetType":"Record Relationship","openUrlIn":"Current Window","Record Relationship":{"targetName":"VoiceCall","targetAction":"view","targetId":"{recordId}","targetRelationship":"RelatedRecords"}},"key":"1755510106778-tp76e38f2","label":"Action","draggable":true,"isOpen":true,"actionIndex":0}],"showSpinner":"false","hideActionIcon":true,"flyoutDetails":{},"styles":{"label":{"textAlign":"center"}}},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12","padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":false,"default":"12"},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{"styles":{"label":{"textAlign":"center"}}},"text":{"align":"","color":""},"inlineStyle":""},"elementLabel":"Action-3","styleObjects":[{"key":0,"conditions":"default","styleObject":{"sizeClass":"slds-size_12-of-12","padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":false,"default":"12"},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{"styles":{"label":{"textAlign":"center"}}},"text":{"align":"","color":""},"inlineStyle":""},"label":"Default","name":"Default","conditionString":"","draggable":false}]}]}},"childCards":["CC_CallLogChild"],"actions":[],"omniscripts":[],"documents":[]},{"fields":[],"conditions":{"id":"state-condition-object","isParent":true,"group":[{"id":"state-new-condition-0","field":"listSize","operator":"==","value":"0","type":"custom","hasMergeField":false}]},"definedActions":{"actions":[]},"name":"Active no record","isSmartAction":false,"smartAction":{},"styleObject":{"padding":[{"type":"around","size":"x-small"}],"margin":[{"type":"bottom","size":"x-small"}],"container":{"class":"slds-card"},"size":{"isResponsive":false,"default":"12"},"sizeClass":"slds-size_12-of-12","class":"slds-card slds-p-around_x-small slds-m-bottom_x-small"},"components":{"layer-0":{"children":[{"name":"Block","element":"block","size":{"isResponsive":false,"default":"12"},"stateIndex":2,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"size":{"isResponsive":false,"default":"12"},"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-p-around_x-small ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"#DDDDDD","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"background-color:#DDDDDD;      \n         "},"children":[{"key":"element_element_block_0_0_block_0_0","name":"Block","element":"block","size":{"isResponsive":false,"default":"2"},"stateIndex":0,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-text-align_center slds-p-around_x-small ","sizeClass":"slds-size_2-of-12 ","size":{"isResponsive":false,"default":"2"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"center","color":""},"inlineStyle":"","style":"      \n         "},"children":[{"key":"element_element_element_block_0_0_block_0_0_flexIcon_0_0","name":"Icon","element":"flexIcon","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","card":"{card}","iconType":"Salesforce SVG","iconName":"standard:record","size":"medium","extraclass":"slds-icon_container slds-icon-standard-record ","variant":"inverse","imgsrc":""},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12 ","size":{"isResponsive":false,"default":"12"}},"parentElementKey":"element_element_block_0_0_block_0_0","elementLabel":"Block-1-Block-0-Icon-0"}],"parentElementKey":"element_block_0_0","elementLabel":"Block-1-Block-0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-text-align_center slds-p-around_x-small ","sizeClass":"slds-size_2-of-12 ","size":{"isResponsive":false,"default":"2"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"center","color":""},"inlineStyle":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Text","element":"outputField","size":{"isResponsive":false,"default":"10"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Ch1%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERegistro%20Chiamata%20(%3C/strong%3E%3Cstrong%3E%3Cspan%20style=%22background-color:%20initial;%22%3E%7BlistSize%7D)%3C/span%3E%3C/strong%3E%3C/span%3E%3C/h1%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_10-of-12 ","size":{"isResponsive":false,"default":"10"}},"elementLabel":"Block-1-Block-1-Text-0","key":"element_element_block_0_0_outputField_1_0","parentElementKey":"element_block_0_0"}],"elementLabel":"Block-0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"size":{"isResponsive":false,"default":"12"},"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-p-around_x-small ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"#DDDDDD","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"background-color:#DDDDDD;      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]}]}},"childCards":[],"actions":[],"omniscripts":[],"documents":[]}],"dataSource":{"type":"IntegrationProcedures","value":{"dsDelay":"","ipMethod":"CC_extractVoiceCall","vlocityAsync":false,"inputMap":{"recordId":"{recordId}"},"jsonMap":"{\"recordId\":\"{recordId}\"}","resultVar":""},"orderBy":{"name":"","isReverse":""},"contextVariables":[]},"title":"CC_CallLog1","enableLwc":true,"isFlex":true,"theme":"slds","selectableMode":"Multi","lwc":{"DeveloperName":"cfCC_CallLog1_1_Unipolsai","Id":"0Rb9O000003tFSvSAM","MasterLabel":"cfCC_CallLog1_1_Unipolsai","NamespacePrefix":"c","ManageableState":"unmanaged"},"Name":"CC_CallLog1","uniqueKey":"CC_CallLog1","Id":"0ko9O000000PLfZQAW","OmniUiCardKey":"CC_CallLog1/Unipolsai/1.0","OmniUiCardType":"Parent"};
  export default definition