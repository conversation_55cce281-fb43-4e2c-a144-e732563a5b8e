<template>
            <div class="slds-grid slds-wrap slds-is-relative containersize">
            <div style="position: fixed;z-index: 999999;top: 0;right: 0;">
  <omnistudio-toast class="flexcard-toast-element" theme="slds" title="" message="" styletype=""> </omnistudio-toast>
</div>
            <div if:true={showLoader} class="slds-is-absolute vloc-loader_override" style="height: 100%; width: 100%; min-height:50px; background: transparent; z-index: 99;">
  <div>
   <omnistudio-spinner
      variant="brand"
      alternative-text="Loading content..."
      size="medium"
      theme="slds"
      ></omnistudio-spinner>
  </div>
</div>
            <template if:false={hasPermission}>
              You don't have required permissions to view this card.
            </template>
            <template if:true={hasPermission}>
              <template if:true={hasRecords}>
                        <template for:each={_records} for:item="record" for:index="rindex">
                            <omnistudio-flex-card-state if:true={record}  key={record._flex.uniqueKey} data-recordid={record.Id} record={record} data-statue="true"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state0element0" class="slds-col   slds-p-around_x-small slds-size_12-of-12 " data-rindex={rindex} style="" >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element0_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3EChiamata%20:%20%7BnameVoiceCall%7D%3C/strong%3E%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element1" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element1_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EData%20e%20ora%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element2" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element2_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7BdataOra%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element3" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element3_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3ETipologia%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element4" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element4_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Btipologia%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element5" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element5_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EProdotto%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element6" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element6_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bprodotto%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element7" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element7_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EAmbito%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element8" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element8_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bambito%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element9" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element9_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EPriorit&agrave;%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element10" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element10_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bpriorita%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element11" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element11_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EEsito%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element12" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element12_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Besito%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                        </template>
                      </template>
                      <template if:false={hasRecords}>
                            <omnistudio-flex-card-state  record={record} data-statue="false"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state0element0" class="slds-col   slds-p-around_x-small slds-size_12-of-12 " data-rindex={rindex} style="" >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element0_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3EChiamata%20:%20%7BnameVoiceCall%7D%3C/strong%3E%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element1" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element1_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EData%20e%20ora%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element2" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element2_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7BdataOra%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element3" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element3_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3ETipologia%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element4" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element4_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Btipologia%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element5" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element5_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EProdotto%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element6" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element6_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bprodotto%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element7" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element7_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EAmbito%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element8" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element8_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bambito%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element9" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element9_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EPriorit&agrave;%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element10" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element10_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bpriorita%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element11" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element11_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EEsito%20:&nbsp;%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element12" class="slds-col   slds-size_6-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element12_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Besito%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                      </template>
            </template>
            <template if:true={hasError}>
            {error}
            </template>
            
      <omnistudio-action action-wrapperclass="slds-hide" re-render-flyout class="action-trigger slds-col" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
      
      </omnistudio-action>
  
            <omnistudio-action class="execute-action" re-render-flyout action-wrapperclass="slds-hide" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
            
            </omnistudio-action>
            </div>
          </template>