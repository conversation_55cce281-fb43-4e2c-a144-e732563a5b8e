let definition =
      {"states":[{"fields":[],"conditions":{"id":"state-condition-object","isParent":true,"group":[]},"definedActions":{"actions":[]},"name":"Active","isSmartAction":false,"smartAction":{},"styleObject":{"padding":[{"type":"around","size":"x-small"}],"margin":[{"type":"around","size":"none"}],"container":{"class":"slds-card"},"size":{"isResponsive":false,"default":"12"},"sizeClass":"slds-size_12-of-12","class":"slds-card slds-p-around_x-small slds-m-bottom_x-small"},"components":{"layer-0":{"children":[{"name":"Block","element":"block","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"padding":[{"type":"around","size":"x-small"}],"class":"slds-p-around_x-small","sizeClass":"slds-size_12-of-12"},"children":[{"key":"element_element_block_0_0_outputField_0_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3EChiamata%20:%20%7BnameVoiceCall%7D%3C/strong%3E%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_12-of-12 ","size":{"isResponsive":false,"default":"12"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-0"},{"key":"element_element_block_0_0_outputField_1_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EData%20e%20ora%20:&nbsp;%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-0-clone-0"},{"key":"element_element_block_0_0_outputField_2_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7BdataOra%7D%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-1-clone-0"},{"key":"element_element_block_0_0_outputField_3_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3ETipologia%20:&nbsp;%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-2-clone-0"},{"key":"element_element_block_0_0_outputField_4_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Btipologia%7D%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-3-clone-0"},{"key":"element_element_block_0_0_outputField_5_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EProdotto%20:&nbsp;%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-4-clone-0"},{"key":"element_element_block_0_0_outputField_6_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bprodotto%7D%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-5-clone-0"},{"key":"element_element_block_0_0_outputField_7_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EAmbito%20:&nbsp;%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-6-clone-0"},{"key":"element_element_block_0_0_outputField_8_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bambito%7D%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-7-clone-0"},{"key":"element_element_block_0_0_outputField_9_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EPriorit&agrave;%20:&nbsp;%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-8-clone-0"},{"key":"element_element_block_0_0_outputField_10_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Bpriorita%7D%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-9-clone-0"},{"key":"element_element_block_0_0_outputField_11_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3EEsito%20:&nbsp;%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-10-clone-0"},{"key":"element_element_block_0_0_outputField_12_0","name":"Text","element":"outputField","size":{"isResponsive":false,"default":"6"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22font-size:%208pt;%22%3E%7Besito%7D%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_6-of-12 ","size":{"isResponsive":false,"default":"6"}},"parentElementKey":"element_block_0_0","elementLabel":"Block-0-Text-11-clone-0"}],"elementLabel":"Block-0"}]}},"childCards":[],"actions":[],"omniscripts":[],"documents":[]}],"dataSource":{"type":null,"value":{},"orderBy":{},"contextVariables":[]},"title":"CC_CallLogChild","enableLwc":true,"isFlex":true,"theme":"slds","selectableMode":"Multi","lwc":{"DeveloperName":"cfCC_CallLog1_1_Unipolsai","Id":"0Rb9O000003tFSvSAM","MasterLabel":"cfCC_CallLog1_1_Unipolsai","NamespacePrefix":"c","ManageableState":"unmanaged"},"isRepeatable":true,"osSupport":true,"multilanguageSupport":true,"listenToWidthResize":true,"Name":"CC_CallLogChild","uniqueKey":"CC_CallLogChild","Id":"0ko9O000000PIJVQA4","OmniUiCardKey":"CC_CallLogChild/Unipolsai/1.0","OmniUiCardType":"Child"};
  export default definition