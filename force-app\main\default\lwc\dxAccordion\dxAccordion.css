.icon-Chiudi {
  color: #0070c0; /* $main-color */
}

.size-l {
  font-size: 40px;
}

.freccia-su {
  transform: rotate(180deg);
}

.accordion-informative-padding {
  padding: 19px 24px;
}

[class^="accordion-"].paddingContainer.desktop {
  --paddingDesktop: 0;
  border: 2px solid #f8f8f0; /* $ivory */
  padding: var(--paddingDesktop);
}

[class^="accordion-"].paddingContainer.tablet {
  --paddingTablet: 0;
  border: 2px solid #f8f8f0;
  padding: var(--paddingTablet);
}

[class^="accordion-"].paddingContainer.mobile {
  --paddingMobile: 0;
  border: 2px solid #f8f8f0;
  padding: var(--paddingMobile);
}

[class^="icon-"] {
  font-family: "unipol-icon" !important;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
}

.accordion-2 .accordion-header {
  height: 50px;
  border: 1px solid #333333; /* $secondary-darkest */
  background-color: white;
  padding: 15px 20px;
}

.accordion-2 .accordion-content {
  padding: 15px 20px;
  background-color: white;
  border: 1px solid #333333;
  border-top: none;
}

.accordion-3 {
  border: 2px solid #d9d9d9; /* $soft-grey */
}

.accordion-4 {
  background: #f8f8f0; /* $ivory */
  padding: 24px 36px;
}

.accordion-4 .icon-Freccia-down {
  color: #0070c0;
}

.accordion-5 {
  background-color: #e6f7ff; /* $light_blue */
}

.accordion-6 {
  background-color: white;
  padding: 24px 36px;
}

.accordion-6 .responsive2colLarge {
  justify-content: flex-start;
}

.accordion-6 .layout-address-autocomplete {
  align-items: center;
  width: 100%;
  max-width: 550px;
}

.accordion-6 .layout-address-autocomplete .w-100 {
  width: 100% !important;
}

.accordion-6 .input-dati-utente {
  max-width: 100%;
}

.accordion-9 {
  border: 2px solid #f0f0f0;
}


.accordion-7 {
  background: var(--blue-primary); 
  margin: 0 auto;
  padding: 24px 16px;
  border-radius: 24px 24px 0 0;
}

.accordion-7-content {
  background: var(--blue-primary); 
  margin: 0 auto;
  padding: 24px 16px;
}

.accordion-7-separator {
  display: block;
  width: 100%;
  height: 1px;
  background-color: #1f5b8e;
  padding: 0 16px;
}

.accordion-7-separator::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  background-color: #1f5b8e; /* $assurance-package-container-color */
  padding: 0 16px;
}

.accordion-7-footer {
  background-color: var(--blue-primary); 
  margin: 0 auto;
  column-gap: 10px;
  border-radius: 0 0 24px 24px;
  align-items: center;
  height: 64px;
}

.accordion-7-footer .icon-Freccia-down {
  color: white;
}

.accordion-7-footer .simple-white {
  background-color: var(--blue-primary);
  border: none;
  margin: 0;
  padding: 0;
}

.accordion-7-footer .simple-white .button-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
  line-height: 1.25;
  cursor: pointer;
  text-decoration: underline;
}

.Accordion-8 {
  max-width: 1062px;
  width: 100%;
  margin: 0 auto;
  background-color: #e2f0f9; /* $secondary-lightest */
  border: 1px solid #8ab5d1; /* $secondary-opaque */
  border-radius: 24px;
  padding: 20px;
}

@media (min-width: 768px) {
  .Accordion-8 {
    padding: 24px;
  }
}

.Accordion-8 .accordionLabel {
  display: flex;
  justify-content: center;
  max-width: fit-content;
  width: 100%;
  margin: 31px auto 0;
  font-size: 18px;
  font-weight: bold;
  color: #0055aa;
  gap: 16px;
  cursor: pointer;
}

.Accordion-8 .accordionLabel::after {
  content: ">";
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: inherit;
  font-weight: inherit;
}

.Accordion-8 .accordionOpenLabel::after {
  transform: rotateZ(-90deg) scaleY(1.5);
}

.Accordion-8 .accordionCloseLabel::after {
  transform: rotateZ(-270deg) scaleY(1.5);
}

.Accordion12 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  background-color: white;
  border-radius: 24px;
  padding: 16px;
  gap: 16px;
}

.Accordion12 .arrowButton {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  gap: 4px;
  cursor: pointer;
  color: #0055aa; 
  font-family: "Unipol Medium";
  font-size: 16px;
}

.Accordion12 .arrowButton > i::before {
  content: "\e962";
}

.AccordionTelematica {
  border: 2px solid #e0e0e0; /* $background-card-disabled */
  padding: 36px 60px;
}

.AccordionTelematica > .AccordionHead {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
}

.AccordionTelematica .IconContainer {
  display: flex;
  flex-direction: row;
  gap: 32px;
}

.AccordionTelematica .PenIcon {
  aspect-ratio: 1 / 1;
  flex-shrink: 0;
  width: 26px;
  background-image: url("https://unipolsai.it/NextAssets/icons/edit_matita.png");
  background-size: cover;
  cursor: pointer;
}

.AccordionTelematica .AccordionContent {
  margin-top: 8px;
}

.AccordionTelematica .AccordionContent button {
  width: 291px;
}

.icon-Freccia-down::after {
  /* content: "" */
}

.arrowButtonText{
  text-decoration: underline;
}

.accordion-7-footer.d-flex {
  display: flex;
}

.accordion-7-footer.jsf-center{
  justify-content: center;
}