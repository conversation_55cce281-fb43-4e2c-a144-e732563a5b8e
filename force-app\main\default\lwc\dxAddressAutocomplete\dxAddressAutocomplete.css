.AddressComponentContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.AddressComponentContainer .hidden {
  display: none !important;
}

.AddressComponentContainer .InputContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: flex-start;
  gap: 4px;
}

.AddressComponentContainer .InputContainer>.InputUtente {
  display: block;
  width: 100%;
  height: 48px;
  padding: 12px 16px;
  background-color: white;
  color: #5c5c5c;
  border: solid 1px var(--blue-primary);
  outline: none;
}

.input-dati-utente {
  padding: 12px 16px;
  width: 100%;
  min-width: 100%;
  height: 48px;
  font-size: var(--font-text-size);
  border-radius: 0;
  background-color: white;
  border: solid 1px var(--main_color);
  color: var(--middle-grey-pu);
}

.input-dati-utente.error {
  border: solid 1px #ff001f !important;
}

.error-message {
  color: red;
  font-size: 12px;
  display: block !important;
}

.icon-Attenzione-pieno {
  color: var(--alert-color);
  font-size: 40px;
}

.bd-icona {
  font-size: 22px;
  color: #ff001f;
  top: 5px;
  position: relative;
  margin-right: 5px;
}

.testo-non-valido {
  height: 14px;
  font-family: var(--font-family-medium);
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.88;
  letter-spacing: normal;
  color: #ff001f;
}


.AddressComponentContainer>.ContainerInserimentoCivico {
  display: flex;
}

.AddressComponentContainer>.ContainerCambioInserimento {
  display: flex;
  flex-direction: row;
  gap: 4px;
}

@media screen and (max-width: 768px) {
  .AddressComponentContainer>.ContainerCambioInserimento {
    flex-direction: column;
  }
}

.AddressComponentContainer>.ContainerInserimentoManuale {
  display: flex;
  flex-direction: column;
  max-width: 500px;
  width: 100%;
  align-self: center;
}

@media screen and (max-width: 768px) {
  .AddressComponentContainer>.ContainerInserimentoManuale {
    gap: 16px;
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .AddressComponentContainer>.ContainerInserimentoManuale {
    gap: 24px;
  }
}

@media screen and (min-width: 1025px) {
  .AddressComponentContainer>.ContainerInserimentoManuale {
    gap: 32px;
  }
}

.hidden {
  display: none !important;
}


/* Styles for Google Maps Autocomplete widget (LWC compatible) */
/* Google autocomplete integrates directly into existing input */

/* Container for manual state buttons */
.button-container {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  justify-content: flex-start;
}

.dropdown-container {
  position: absolute;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ccc;
  border-top: none;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  top: 100%;
  /* Posiziona il dropdown sotto l'input */
  left: 0;
}

.input-wrapper {
  position: relative;
  /* Necessario per posizionare correttamente il dropdown */
  width: 100%;
}

.dropdown-item {
  position: relative;
  padding-left: 36px;
  /* Spazio a sinistra per l'icona */
  display: flex;
  flex-direction: row;
  /* Mette gli elementi figli sulla stessa riga */
  align-items: baseline;
  /* Allinea il testo sulla baseline */
  gap: 4px;
  /* Aggiunge un piccolo spazio tra testo principale e secondario */
  min-height: 44px;
}

.dropdown-item:before {
  content: '';
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="%235B5B5B"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.prediction-main,
.prediction-secondary {
  /* Assicurati che il font Unipol sia definito e caricato, 
     sostituisci 'Unipol-Font-Name' con il nome corretto del font.
     'Arial, sans-serif' sono fallback. */
  font-family: 'Unipol-Font-Name', Arial, sans-serif;
  /* Usa una variabile CSS per il blu Unipol se disponibile, altrimenti un fallback */
  color: var(--unipol-interactive-blue, #193A56);
  position: relative;
  /* Assicura che il testo sia posizionato correttamente */
  z-index: 0;
}

.prediction-main {
  font-weight: bold;
  /* Mantiene il testo principale in grassetto */
}

.prediction-secondary {
  font-size: 0.9em;
}

/* Stile per "Powered by Google" */
.powered-by-google {
  text-align: right;
  padding: 5px 10px;
  font-size: 11px;
  color: #5f6368;
  border-top: 1px solid #eee;
}

.powered-by-google img {
  height: 14px;
  vertical-align: middle;
  margin-left: 4px;
}