.CardProtezioneContainer {
  display: flex;
  flex-direction: column;
  border: 2px solid var(--border-card-disabled); 
  border-radius: 24px;
  gap: 12px;
  padding: 24px;
  height: auto;
  max-width: calc(100vw - 32px);
}

.CardProtezioneContainer .CardProtezioneHeader {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  font-weight: bold;
  color: var(--main_color);
  justify-content: space-between;
}

.CardCheckbox {
  display: flex;
  flex-direction: row;
  gap: 4px;
  font-weight: bold;
  color: var(--main_color);
  justify-content: flex-end;
  margin-left: auto;
}

.CardProtezioneContainer .CardProtezioneHeader .CardProtezioneTitolo {
  color: var(--blue-primary);
  font-family: var(--font-family-bold);
  font-size: 16px;
}

.CardProtezioneContainer .CardProtezioneBody {
  color: var(--blue-primary);
  font-family: var(--font-family-medium);
  font-size: 13px;
}

.CardProtezioneContainer .CardProtezioneFooter {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
}

.CardCurrency {
  display: flex;
  flex-direction: row;
  gap: 4px;
  font-weight: bold;
  color: var(--main_color);
  justify-content: right;;
}

.PremioRataScontato {
  font-size: 18px;
  font-weight: bolder;
}

.TestoPrezzoGaranzia {
  font-size: 12px;
  font-family: emoji;
  font-weight: normal;
}

.CardProtezioneContainer .CardProtezioneFooter .CardProtezioneFooterValori {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.CardProtezioneContainer .CardProtezioneFooter .CardProtezioneFooterValori .ValoreElement {
  display: flex;
  flex-direction: row;
  gap: 4px;
  font-family: var(--font-family-bold);
  color: var(--blue-primary);
  font-size: 13px;
}

.CardProtezioneContainer .CardProtezioneFooter .CardProtezioneFooterValori .ValoreElement .mrg-label-input {
  margin: 0 !important;
  padding: 0 !important;
  line-height: unset;
}

.CardProtezioneContainer .CardProtezioneFooter .CardProtezionePremioLordo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.CardProtezioneContainer .CardProtezioneFooter .CardProtezionePremioLordo .PremioLordo {
  color: var(--blue-primary);
  font-family: var(--font-family-bold);
  font-size: 18px;
}

.CardProtezioneContainer .CardProtezioneFooter .CardProtezionePremioLordo .Occorrenza {
  color: var(--blue-primary);
  font-family: var(--font-family-default);
  font-size: 11px;
}

@media (max-width: 767px) {
  .CardProtezioneContainer {
    width: 340px;
  }
  .CardProtezioneContainer .CardProtezioneFooter {
    margin-top: 2px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .CardProtezioneContainer {
    width: 340px;
  }
  .CardProtezioneContainer .CardProtezioneFooter {
    margin-top: 8px;
  }
}

@media (min-width: 1025px) {
  .CardProtezioneContainer {
    width: 360px;
  }
  .CardProtezioneContainer .CardProtezioneFooter {
    margin-top: 18px;
  }
}

.CardProtezioneDetails {
  display: flex;
  flex-direction: row;
  gap: 4px;
  font-weight: bold;
  color: var(--main_color);
  justify-content: right;
}