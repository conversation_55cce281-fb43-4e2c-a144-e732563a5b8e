.CarouselCardContainer {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  width: 340px;
  height: 350px;
  max-height: 350px;
  gap: 16px;
  padding: 24px;
  margin-right: 8px;
}

.CarouselCardHead {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.CarouselCardHeadIcon {
  aspect-ratio: 1 / 1;
  display: block;
  width: 40px;
  background-size: cover;
  filter: brightness(0) invert(1);
}

.CarouselCardBody {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
}

.CarouselCardBodyContent {
  overflow-y: hidden; 
  max-height: 125px;
  flex-shrink: 1;
}

.CarouselCardButton {
  margin-top: auto;
}

@media screen and (max-width: 768px) {
  .CarouselCardContainer {
    width: calc(95vw - 68px);
  }
}

.bg-Veicoli,
.bg-Mobilità,
.bg-Mo<PERSON>ita {
  background-color: #0169b4;
}

.bg-<PERSON>,
.bg-Famiglia,
.bg-Casa,
.bg-<PERSON><PERSON><PERSON>,
.bg-<PERSON><PERSON> {
  background-color: #e94e10;
}

.bg-Infort<PERSON>,
.bg-Salute,
.bg-<PERSON>att<PERSON> {
  background-color: #59a627;
}