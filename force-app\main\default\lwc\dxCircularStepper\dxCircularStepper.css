 .container {
    height: 80px;
    width: 80px;
    background-color: cyan;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    border-radius: 10px;
    display: grid;
    place-items: center;
  }
  
  /* Stile per l'indicatore circolare */
  .progressCircular {
    --statusProgress: 180deg;
    height: 48px;
    width: 48px;
    background: conic-gradient(
      #38c661 var(--statusProgress),
      #cccccc var(--statusProgress) 360deg
    );
    position: relative;
    border-radius: 50%;
    display: grid;
    place-items: center;
  }
  
  /* Cerchio interno per creare l'effetto "vuoto" */
  .progressCircular::before {
    content: "";
    height: 80%;
    width: 80%;
    background-color: white;
    position: absolute;
    border-radius: 50%;
  }
  
  /* Stile del testo che mostra il progresso (currentStep/totalStep) */
  .value {
    color: #193a56;
    position: absolute;
    font-weight: bold;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  
  /* Se ti serve un ulteriore livello di styling simile a .inner-circle in Angular */
  .inner-circle {
    position: relative;
    border: 10px solid #cccccc;
    border-radius: 50%;
    padding: 25px;
  }