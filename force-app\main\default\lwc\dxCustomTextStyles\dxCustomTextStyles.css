/* Visualizzazione per dispositivi mobili */
.visible-mobile {
    display: block;
    width: 100%;
}

@media only screen and (min-width: 768px) {
    .visible-mobile {
        display: none;
    }
}

/* Visualizzazione per tablet */
.visible-tablet {
    display: none;
}

@media only screen and (min-width: 768px) and (max-width: 1023px) {
    .visible-tablet {
        display: block;
        width: 100%;
    }
}

/* Visualizzazione per desktop */
.visible-desktop {
    display: none;
}

@media only screen and (min-width: 1024px) {
    .visible-desktop {
        display: block;
        width: 100%;
    }
}