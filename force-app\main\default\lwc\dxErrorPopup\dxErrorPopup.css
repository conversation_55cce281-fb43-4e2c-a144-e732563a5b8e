@font-face {
  font-family: 'Unipol';
  src: url("/resource/unipolFonts/Apex_New_Book.woff") format('woff');
  font-style: normal;
  font-weight: normal;
}

@font-face {
  font-family: 'Unipol Bold';
  src: url("/resource/unipolFonts/Apex_New_Bold.woff") format('woff');
  font-style: normal;
  font-weight: bold;
}

.modale-errore-container {
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modale-errore-container .modale-errore-view > * {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  max-width: calc(100% - 32px);
  width: 840px;
  padding: 56px 120px;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  background-color: white;
}

@media (max-width: 768px) {
  .modale-errore-container .modale-errore-view > * {
    padding: 32px 20px;
  }
}

.modal-icon {
  aspect-ratio: 1;
  display: block;
  height: 80px;
  width: 80px;
}

@media (max-width: 768px) {
  .modal-icon {
    height: 40px;
    width: 40px;
  }
}

.modale-errore-titolo {
  font-family: "Unipol Bold";
  font-size: 24px;
  color: #193a56;
  text-align: center;
}

.modale-errore-contenuto {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  font-family: "Unipol";
  font-size: 16px;
  color: #193a56;
  text-align: center;
}

.modale-errore-contenuto span b {
  font-weight: 700;
  font-family: "Unipol Bold";
}

.modale-errore-button {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 290px;
  width: 100%;
  height: 50px;
  background-color: #c4151c;
  margin: 0 auto;
  cursor: pointer;
  font-family: "Unipol Bold";
  font-size: 18px;
  text-align: center;
  color: white;
}

.negativo {
  opacity: 0.9;
  background-color: #192a56 !important;
}

.container-pulsanti {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  max-width: 90%;
  width: 100%;
  gap: 12px;
}

@media (max-width: 768px) {
  .container-pulsanti {
    grid-template-columns: 1fr;
    max-width: 100%;
  }
}