.visible-desktop {
  display: none;
  @media (min-width: 1025px) {
    display: block;
  }
}

.visible-tablet {
  display: none;
  @media (min-width: 768px) and (max-width: 1024px) {
    display: block;
  }
}

.visible-mobile {
  display: none;
  @media (max-width: 767px) {
    display: block;
  }
}

button {
  background: none;
}

.centerButton {
  margin: 0 auto;
  align-self: center;
}

.Positive-Button,
.PositiveTPD-Button,
.Full-Positive-Button {
  width: 206px;
  height: 50px;
  background-color: var(--main_red);
  object-fit: contain;
  cursor: pointer;
  border: none;
}


.Positive-Button.disabled,
.PositiveTPD-Button.disabled,
.Full-Positive-Button.disabled {
  opacity: 0.3;
  cursor: default;
}

.Positive-Button .button-text,
.PositiveTPD-Button .button-text, 
.Full-Positive-Button .button-text {
  font-family: var(--font-family-bold);
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: white;
}

.Full-Positive-Button {
  width: 100%;
  min-width: 240px;
  max-width: 100%;
  text-align: center !important;
}

.Cancel-button,
.Full-Cancel-Button,
.Full-Cancel-button {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 260px;
  height: 50px;
  background-color: var(--dark-light_blue);
  object-fit: contain;
  cursor: pointer;
  border: none;
}

.Full-Cancel-Button.disabled,
.Full-Cancel-button.disabled,
.Cancel-button.disabled {
  opacity: 0.3;
}

.Cancel-button .button-text,
.Full-Cancel-Button .button-text,
.Full-Cancel-button .button-text {
  font-family: var(--font-family-bold);
  font-size: var(--font-text-size);
  font-weight: 700;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: white;
}

.Full-Cancel-Button,
.Full-Cancel-button {
  margin-top: 0;
  margin-bottom: 0;
  width: 100%;
  min-width: 240px;
}

.Community-buttons,
.CommunityTPD-buttons {
  width: 500px;
  height: 100px;
  border: white;
  box-shadow: 0 2px 20px 2px rgba(26, 26, 26, 0.16);
  background-color: white;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.Community-buttons .button-text,
.CommunityTPD-buttons .button-text {
  font-weight: normal;
  font-family: var(--font-family-default);
  color: var(--main_color);
  font-size: var(--font-text-size);
}

.Back {
  background-color: transparent;
  border: none;
}

.Back .button-text {
  font-family: var(--font-family-bold);
  font-size: var(--font-text-size);
  color: var(--main_color);
  font-weight: 500;
  line-height: 1.25;
  cursor: pointer;
  text-decoration: underline;
}

.Simple,
.SimpleTPD,
.SimpleGrey,
.SimpleSmall,
.Simple-NegativeApp,
.SimpleTPDBold {
  background-color: transparent;
  border: none;
  margin: 0;
  padding: 0;
}

.Simple,
.SimpleTPD,
.SimpleGrey,
.SimpleSmall,
.Simple-NegativeApp,
.SimpleTPDBold .button-text {
  font-family: var(--font-family-medium);
  font-size: var(--font-text-size);
  color: var(--main_color);
  font-weight: 500;
  line-height: 1.25;
  cursor: pointer;
  text-decoration: underline;
}

.SimpleGrey .button-text {
  color: var(--middle-grey-pu);
}

.SimpleSmall .button-text {
  font-family: var(--font-family-medium);
  color: var(--main_color);
  font-size: var(--font-text-xs-mobile-size);
}

.AssuranceButton {
  background-color: transparent;
  border: none;
}

.AssuranceButton .button-text {
  font-family: var(--font-family-bold);
  font-size: var(--font-text-size);
  color: var(--main_color);
  font-weight: 500;
  line-height: 1.25;
  cursor: pointer;
  text-decoration: underline;
}

.Highlight {
  width: 206px;
  height: 50px;
  border: solid 0 #979797;
  background-color: white;
  border-bottom-color: var(--sub-button-color);
  border-bottom-width: 6px;
}

.Highlight .button-text {
  color: var(--main_color);
  font-family: var(--font-family-bold);
  font-size: var(--font-text-promo-size);
  cursor: pointer;
}

.Simple-Negative-Carousel {
  display: flex;
  justify-content: center;
  align-items: center;

  height: 50px;
  border: solid 0 #979797;
  background-color: white;

  border-bottom-color: var(--sub-button-color);
  border-bottom-width: 6px;

  width: 208px;
}

.Simple-Negative-Carousel .button-text {
  color: var(--main_color);
  font-family: var(--font-family-bold);
  font-size: var(--font-text-promo-size);
  cursor: pointer;
}

.Rounded-positive-button {
  background-color: var(--main_red);
  border-radius: 25px;
  border: none;

  width: 206px;
  height: 45px;

  &.disabled {
    cursor: default;
    opacity: 0.3;

    .button-text {
      cursor: default;
    }
  }
}

.Rounded-positive-button .button-text {
  color: white;
  font-family: var(--font-family-bold);
  font-size: var(--font-text-size);
  cursor: pointer;
}

.Rounded-cancel-button {
  background-color: white;
  border-radius: 25px;
  border: none;

  width: 206px;
  height: 45px;
}

.Rounded-cancel-button .button-text {
  color: var(--main_color);
  font-family: var(--font-family-bold);
  font-size: var(--font-text-size);
  cursor: pointer;
}

.mr-10 {
  margin-right: 10px;
}

.DocumentationButton {
  padding: 19px 16px;
  margin: 15px 0;
  background-color: white;
  border: solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  width: var(--width_container_desktop);
}

.DocumentationButton .button-text {
  color: var(--main_color);
  font-family: var(--font-family-bold);
  font-size: var(--font-text-size);
}

.download {
  width: 92px;
  height: 40px;
  font-family: var(--font-family-default);
  color: white;
  background-color: var(--medium-light-blue);
  border: none;
}

.icon-pdf-size {
  font-size: 40px !important;
  margin-right: 12px;
  color: var(--dot_slick_bg);

  display: block;
}

.Footer-button-WEB {
  width: 280px;
  height: 45px;
  padding: 13px 0.3px 13px 0.7px;
  background-color: var(--light-blue-color);
  border: none;
  cursor: pointer;
}

.Footer-button-WEB .button-text {
  color: var(--dark-light_blue);
  font-family: var(--font-family-bold);
  font-size: var(--font-text-size);
}

.Footer-button-WEB.disabled {
  opacity: 0.3;
  cursor: default;
}

.SimpleTPD-responsive-white {
  background-color: transparent;
  border: none;
  margin: 0;
  padding: 0 1em 0 0;
}

.SimpleTPD-responsive-white .button-text {
  font-family: var(--font-family-medium);
  color: white;
  font-weight: 500;
  line-height: 1.25;
  cursor: pointer;
  text-decoration: underline;

  font-size: var(--font-text-responsive-desktop-size);
}
.DiscountButton {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  outline: unset !important;
}

.DiscountButton::after {
  /* content: ">";
  color: var(--blue-primary);
  font-size: inherit;
  transform: scaleY(2) scaleX(0.5); */

  content: "";
  display: block;
  aspect-ratio: 1 / 1;
  flex-shrink: 0;
  width: 20px;
  background-image: var(--after-icon);
  background-repeat: no-repeat;
}

.ButtonFrazionamento {
  border: 1px solid var(--border-card-disabled);
  background-color: white;
  border-radius: 12px;
  padding: 8px;
  flex-grow: 1;

  width: 260px;
}

.ButtonFrazionamento.frazionamentoSelected {
  border: unset;
  background-color: var(--main_color);
  position: relative;
}

.ButtonFrazionamento.frazionamentoSelected::after {
  content: "";

  aspect-ratio: 1 / 1;
  display: block;
  background-color: inherit;
  z-index: -100;

  width: 20px;
  position: absolute;
  left: 50%;
  bottom: 0;

  transform-origin: center;
  transform: translateY(50%) translateX(-50%) rotateZ(45deg);
}

.button-center-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;

  --after-icon: url('/resource/1754569021000/interprete_icons/interprete_icon/FrecciaDestraIcon.svg');
}

.button-seleziona-agenzia {
  border-radius: 25px;
  border: none;
  color: #fff;
  text-align: center;
  font-family: Unipol Bold;
  font-size: 16px;
  background-color: #c4151c;
  min-width: 200px;
  min-height: 48px;
}