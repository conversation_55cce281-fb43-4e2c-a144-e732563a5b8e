.pxTextInput {
}

.debug {
  border: 1px solid #e7a11f;
}

.temporaryLabel {
  color: #e7a11f;
  font-style: italic;
}

.visible-desktop {
  display: none;
}  
@media (min-width: 1025px) {
  .visible-desktop {    
    display: block;
  }
}

.visible-tablet {
  display: none;
}
@media (min-width: 768px) and (max-width: 1024px) {
  .visible-tablet {
    display: block;
  }
}

.visible-mobile {
  display: none;
}
@media (max-width: 767px) {
  .visible-mobile {
    display: block;
  }
}

.mrg-label-input {
  margin-bottom: 4px;
}

.input-dati-utente {
  padding: 12px 16px;
  width: 100%;
  min-width: 100%;
  height: 48px;
  font-size: var(--font-text-size);
  border-radius: 0;
  background-color: white;
  border: solid 1px var(--main_color);
  color: var(--middle-grey-pu);
}

.disable-input {
  border-color: var(--ivory) !important;
  background-color: #f1f1f1 !important;
  color: var(--middle-grey);
  opacity: 1;
  cursor: not-allowed;
  pointer-events: none;
}

.icon-Attenzione-pieno {
  color: var(--alert-color);
  font-size: 40px;
}

.bd-icona {
  font-size: 22px;
  color: #ff001f;
  top: 5px;
  position: relative;
  margin-right: 5px;
}

.invalid-input {
  border: 1px solid red;
}

.testo-non-valido {
  height: 14px;
  font-family: var(--font-family-medium);
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.88;
  letter-spacing: normal;
  color: #ff001f;
}

.error-message {
  display: flex;
  align-items: center;
  height: 30px;
}