.paragraph {
  color: var(--dark-light_blue);
}

.debug {
  border: 1px solid #e7a11f;
}

.temporaryLabel {
  color: #e7a11f;
  font-style: italic;
}

.visible-desktop {
  display: none;  
}
@media (min-width: 1025px) {
  .visible-desktop {
    display: block;
  }
}

.visible-tablet {
  display: none;
}
@media (min-width: 768px) and (max-width: 1024px) {
  .visible-tablet {
    display: block;
  }
}

.visible-mobile {
  display: none;
}
@media (max-width: 767px) {
  .visible-mobile {
    display: block;
  }
}

.paragraph ul {
  margin-top: 0;
  margin-bottom: 10px;
  padding-left: 20px;
  list-style: disc;
}

.paragraph li {
  font-family: var(--font-family-default);
  font-size: 16px;
  line-height: 1.5;
}