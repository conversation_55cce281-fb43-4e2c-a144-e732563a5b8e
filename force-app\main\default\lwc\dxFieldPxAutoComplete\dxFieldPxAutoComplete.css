.debug {
  border: 1px solid #c44754;
}

.temporaryLabel {
  color: #c44754;
  font-style: italic;

}

.slds-dropdown.slds-dropdown_length-with-icon-7 {
    height: 90px;
}


.slds-listbox__option {
    padding: 8px 12px;
    cursor: pointer;
}

.tpd_selectAngular {
  max-width: 500px;
  position: relative;
  font-family: var(--font-family-default);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.25;
  height: 48px;
}

.tpd_selectAngular.isDisabled {
  cursor: default !important;
}

.select-styled {
  color: #5c5c5c !important;
  position: absolute;
  inset: 0;
  background-color: #fff;
  padding: 6px 12px !important;
  border: 1px solid var(--color-darker);
  text-align: left;
  word-break: break-word;
  overflow: hidden;
  display: flex;
  align-items: center;
  height: 48px;
  font-family: var(--font-family-medium);
}

.select-styled > .image-container{
  display: block;
  width: 100px;
  height: 60px;

  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: lightgray;

  flex-shrink: 0;
  margin-right: 8px;
}

.select-styled.isDisabled {
  pointer-events: none;
  color: #9b9b9b;
  border: 0;
}

.select-styled.disabled-input-box {
  background-color: #f1f1f1 !important;
  color: #9b9b9b !important;
  font-size: 16px;
  font-family: Unipol;
  line-height: 24px;
  height: 48px !important;
  pointer-events: none;
  cursor: not-allowed;
}

.select-styled > input {
  display: block;
  width: 100%;
  height: 100%;
  border: 0;
  font-size: inherit;
  font-family: inherit;
  color: inherit;
  outline: none;
}

.select-styled::after {
  border: none !important;
  transform: rotate(135deg) !important;
  width: 11px !important;
  cursor: pointer;
  display: inline-block;
  height: 11px !important;
  border-style: solid !important;
  border-width: 2px 2px 0 0 !important;
  position: absolute;
  top: 17px !important;
  right: 17px !important;
  content: "";
}

.select-styled.active::after {
  transform: rotate(-45deg) !important;
  top: 19px !important;
}

.select-options {
  border: 1px solid var(--color-darker);
  border-top: none !important;
  top: 96% !important;

  position: absolute;
  right: 0;
  left: 0;
  z-index: 995;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color: #fff;
  text-align: left;
  max-height: 265px;
  overflow: auto;
  overflow-x: hidden;
  display: none;
}

.opened {
  display: block;
}


.select-options > li {
  color: #5c5c5c !important;
  margin: 0;
  padding: 12px 16px !important;
  border-bottom: none !important;
  border-left: none !important;
  border-right: none !important;
  border: 1px solid #c1c1c1;
  height: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.select-options > li > .image-zone {
  display: block;
  width: 100px;
  height: 60px;

  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: lightgray;

  flex-shrink: 0;
}

.select-options > li:hover {
  background: #e2f0f9;
}

.icon-Attenzione-pieno {
  color: var(--alert-color);
  font-size: 40px;
}

.bd-icona {
  font-size: 22px;
  color: #ff001f;
  top: 5px;
  position: relative;
  margin-right: 5px;
}

.invalid-input {
  border: 1px solid red;
}

.testo-non-valido {
  height: 14px;
  font-family: var(--font-family-medium);
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.88;
  letter-spacing: normal;
  color: #ff001f;
}