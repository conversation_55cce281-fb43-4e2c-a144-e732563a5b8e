.pxRadioButtons {
  padding: 10px;
  margin: 5px 0;
}

.debug {
  border: 1px solid #cd86ba;
}

.temporaryLabel {
  border: 1px solid #cd86ba;
  font-style: italic;
  color: #cd86ba;
}

.visible-desktop {
    display: none;
  
    @media (min-width: 1025px) {
      display: block;
    }
  }
  
.visible-tablet {
    display: none;

    @media (min-width: 768px) and (max-width: 1024px) {
        display: block;
    }
}

.visible-mobile {
    display: none;

    @media (max-width: 767px) {
        display: block;
    }
}

/********************
PACKAGE RADIO BUTTONS
********************/

.radio-layout {
  display: flex;
  flex-direction: row;
  gap: 2.4rem;
  justify-content: center;
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.package-radio-option {
  cursor: pointer;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  gap: 16px;
  width: 240px;
  height: 96px;
  background: #ffffff;
  border: 1px solid var(--border-card-disabled);
  border-radius: 24px;
  position: relative;
  color: #193a56;
  font-family: var(--font-family-bold);
  text-transform: uppercase;
  font-size: var(--font-text-size);
}

@media screen and (max-width: 767px) {
  .package-radio-option {
    font-size: var(--font-text-responsive-mobile-size);
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .package-radio-option {
    font-size: var(--font-text-responsive-tablet-size);
  }
}

@media (min-width: 1025px) {
  .package-radio-option {
    font-size: var(--font-text-responsive-desktop-size);
  }
}

.package-radio-option:has(input[type="radio"]:checked) {
  background-color: #193a56;
  border-color: #193a56;
  color: #fff;
} 

.radio-input-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-clip: content-box;
}

.radio-input-container:has(input[type="radio"]:checked) {
  background-color: var(--color-green);
  border: 2px solid #ffffff;
}

.radio-input-container:has(input[type="radio"]:checked)::after {
  top: 100%;
  left: 50%;
  border-color: var(--blue-primary) transparent transparent transparent;
  margin-left: -12px;
  border-width: 12px;
}

.radio-input-container::after {
  content: "";
  position: absolute;
  border-style: solid;
  border: solid transparent;
  height: 0;
  width: 0;
}

.radio-input-container > input[type="radio"]:checked {
  appearance: none;
  border: none;
  padding: 0;
  aspect-ratio: 1 / 1;
  content: "";
  background-color: #fff;
  width: 100%;
  clip-path: polygon(28% 38%, 41% 53%, 75% 24%, 86% 38%, 40% 78%, 15% 50%);
}

.radio-input-container > input[type="radio"] {
  /* appearance: none; */
  cursor: pointer;
  display: inline-block;
  width: 24px;
  height: 24px;
  padding: 2.5px;
  background-clip: content-box;
  border: 2px solid var(--color-darker);
  border-radius: 50%;
  margin: 0;
}

.card-content-mensile, .card-content-annuale {
    margin-top: 20px;
    margin-bottom: 3px;
}

.no-wrap-content {
  max-width: 120px; 
  white-space: nowrap;
}

.horizontal-layout {
  display: flex;
  flex-direction: row;
}

.vertical-layout {
  display: flex;
  flex-direction: column;
}




/*******************************
FLAT CARD INVERTED RADIO BUTTONS
*******************************/
.FlatInvertedCardContainer {
  display: flex !important;
  flex-direction: column;
  gap: 16px;
}

.FlatInvertedCard {
  --cardBorder: var(--border-card-disabled);
  --cardBackground: #fff;
  --textColor: var(--blue-primary);

  border: solid 1px var(--cardBorder);
  background-color: var(--cardBackground);
  color: var(--textColor);
  flex-grow: 1;

  min-height: auto !important;
  height: auto !important;
}

.FlatInvertedCard:has(label input[type="radio"]:checked) {
  --cardBorder: var(--blue-primary);
  --cardBackground: var(--blue-primary);
  --textColor: #fff;
}

.FlatInvertedCard > label {
  --markerBorderColor: var(--blue-primary);
  --markerBackgroundColor: #fff;
  --markerBorderSize: 2px;

  /* Label che contiene i testi */
  display: flex;
  align-items: center;
  font-size: 16px;
  gap: 8px;
  cursor: pointer;
  width: 100%;
  height: auto !important;
  min-height: auto !important;
  padding: 16px;
}

/* TODO HANDLE RADIO BUTTON CHECK SAME AS PACKAGE */
.FlatInvertedCard > label:has(input[type="radio"]:checked) {
  --markerBorderColor: var(--color-green);
  --markerBackgroundColor: var(--color-green);
  --markerBorderSize: 0;
}

.marker {
  aspect-ratio: 1 / 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  background-color: var(--markerBackgroundColor);
  border: solid var(--markerBorderSize) var(--markerBorderColor);
  border-radius: 300px;
  width: 16px;
  height: 16px;
}

.marker > input[type="radio"] {
  /* proprietà per nascodere input html */
  appearance: none;
  cursor: pointer;
  
}

/* .marker::after { */
.marker > input[type="radio"]:checked {
  aspect-ratio: 1 / 1;
  content: "";
  background-color: #fff;
  width: 100%;
  clip-path: polygon(28% 38%, 41% 53%, 75% 24%, 86% 38%, 40% 78%, 15% 50%);
}

.GreenCheckRadioContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
}

.GreenCheckRadioItem {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  cursor: pointer;
}

.GreenCheckRadioItem > input[type="radio"] {
  display: none;
}

.GreenCheck {
  aspect-ratio: 1/1;

  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid var(--blue-primary);
  border-radius: 50%;

  width: 24px;
}

.GreenCheck::after {
  content: '';
  aspect-ratio: 4 / 2;
  border: 2px solid white;
  border-top: 0;
  border-right: 0;
  width: 30%;
  transform: rotateZ(-45deg);
}

.GreenCheckRadioItem:has(input[type="radio"]:checked) > .GreenCheck {
  background-color: var(--check-green-color);
  border-color: var(--check-green-color);
}

.GreenCheckContentContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}



/*******************
RADIO CARD CONTAINER
*******************/
.radio-card-container {
  display: flex;
  flex-direction: column;
  gap: 16px
}

.radio-card {
  height: 56px;
    width: 100%;
    padding: 16px;
    border: solid 1px var(--border-card-disabled);
    cursor: pointer;
    
}

.radio-card > label {
  font-size: 16px;
  color: var(--middle-grey-pu)
}

.radio-card:has(input[type="radio"]:checked) {
  background-color: var(--blue-primary);
  border: 0;
}

.radio-card:has(input[type="radio"]:checked) > label {
  color: #fff;
}

.radio-card-container > input.not-visible {
  display: none;
}


/*******************
FLAT CARD 
*******************/

/* Converted CSS for FlatCard LWC */
.FlatCardContainer {
  gap: 16px;
  flex-shrink: 0 !important;
  display: flex;
}

.FlatCard {
  --cardBorder: #ccc; /* Replace with $border-card-disabled */
  --cardBackground: #fff; /* Replace with $white */
  --textColor: #007bff; /* Replace with $blue-primary */

  border: solid 1px var(--cardBorder);
  background-color: var(--cardBackground);
  color: var(--textColor);

  min-height: auto !important;
  height: auto !important;
}

/* Gestione dello stato checked per la card */
.FlatCard:has(input[type="radio"]:checked) {
  --cardBorder: #193a56;
  --cardBackground: #193a56;
  --textColor: #fff; /* white */
}

.FlatCard label {
  --markerBorderColor: #193a56; 
  --markerBackgroundColor: #fff; 
  --markerBorderSize: 2px;
  
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  gap: 8px;
  cursor: pointer;
  min-height: auto !important;
  height: auto !important;
  padding: 16px;
}

/* Div che contiene il titolo e sottotitolo */
.FlatCard label > div {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Gestione stato checked per il marker */
.FlatCard label:has(input[type="radio"]:checked) {
  --markerBorderColor: #8bc94d; /* color-green */
  --markerBackgroundColor: #8bc94d; /* color-green */
  --markerBorderSize: 0;
}

.FlatCard label input[type="radio"] {
  /* Nascondiamo il radio */
  display: none;
}

.FlatCard .marker{
  background-color: var(--markerBackgroundColor);
  border: solid var(--markerBorderSize) var(--markerBorderColor);
  border-radius: 300px;
  width: 20px;
  height: 20px;
  aspect-ratio: 1 / 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}



/* Checkmark icon quando selezionato */
.FlatCard .marker::after {
  /* Checkmark icon */
  content: "";
  aspect-ratio: 1 / 1;
  background-color: #fff; /* white */
  width: 100%;
  clip-path: polygon(28% 38%, 41% 53%, 75% 24%, 86% 38%, 40% 78%, 15% 50%);
}




