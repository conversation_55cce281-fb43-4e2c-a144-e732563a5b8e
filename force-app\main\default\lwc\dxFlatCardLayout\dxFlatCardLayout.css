/* Converted CSS for FlatCardLayout */

.FlatCardLayoutContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 10px;
    padding: 16px;
    align-items: center;
    background-color: var(--blue-primary);
    margin: 32px auto;
    max-width: 528px;
  }
  
  .FlatCardLayoutContent {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .FlatCardLayoutCheck {
    aspect-ratio: 1 / 1;
    display: block;
    position: relative;
    width: 20px;
    background-color: var(--check-green-color);
    border-radius: 50%;
    cursor: pointer;
    flex-shrink: 0;
  }
  
  .FlatCardLayoutCheck::after {
    content: "";
    aspect-ratio: 3 / 2;
    width: 40%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -55%) rotate(-45deg);
    border-left: 3px solid white;
    border-bottom: 3px solid white;
  }
  