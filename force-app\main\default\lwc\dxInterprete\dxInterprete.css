@font-face {
  font-family: 'Unipol';
  src: url("/resource/unipolFonts/Apex_New_Book.woff") format('woff');
  font-style: normal;
  font-weight: normal;
}

@font-face {
  font-family: 'Unipol Medium';
  src: url("/resource/unipolFonts/Apex_New_Medium.woff") format('woff');
  font-style: normal;
  font-weight: normal;
}

@font-face {
  font-family: 'Unipol Bold';
  src: url("/resource/unipolFonts/Apex_New_Bold.woff") format('woff');
  font-style: normal;
  font-weight: bold;
}

@font-face {
  font-family: 'unipol-icon';
  src: url("/resource/unipolFonts/unipol-icon.ttf") format('truetype');
}

.interprete {
  padding-bottom: 150px;

  --ivory: #f1f1f1;

  --font-family-default: "Unipol";
  --font-family-bold: "Unipol Bold";
  --font-family-medium: "Unipol Medium";
  --font-family-icon: "unipol-icon";

  /* --font-family-default: "arial";
  --font-family-bold: "arial";
  --font-family-medium: "arial"; */

  --font-text-size: 16px;
  
  --font-text-xs-mobile-size: 13px;
  --font-text-responsive-mobile-size: 16px;
  --font-text-responsive-tablet-size: 18px;
  --font-text-responsive-desktop-size: 20px;

  --circle-size: 18px;
  --inner-circle-size: 6px;


  --alert-color: #ff001f;
  --main_color: #141351;
  --color-darker: #0f3250;
  --main_red: #c5151b;
  --blue-primary: #193a56;
  --light-blue-color: #446b9e;
  --medium-light-blue: #334966;
  --dark-light_blue: #0f324f;
  --middle-grey: #666666;
  --middle-grey-pu: #5c5c5c;
  --sub-button-color: #d0021b;
  --dot_slick_bg: #f1f1f1;
  --border-card-disabled: #cccccc4a;
  --check-green-color: #38c661;
  --check-disabled-color: #cbdeea;
  --check-green-disabled-color: #c3e3a3;
  --check-error-color: #f40053;
  --color-green: #8bc94d;
  --width_container_mobile: 100%;
  --width_container_tablet: 90vw;
  --width_container_desktop: 75vw;
  --selected-radio: #1285bf;
  --secondary-lightest: #e2f0f9;

  --base-path: "https://unipolsai.it/NextAssets/"
}

.interprete .spinner {
  height: 100svh;
  width: 100svw;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.background-Secondary {
  background-color: var(--main_color);
}