.locator-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.map-section {
  flex: 1;
  min-height: 400px;
  position: relative;
}

.map-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.agencies-section {
  background: white;
  border-top: 1px solid #e0e0e0;
  max-height: 300px;
  overflow-y: auto;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: bold;
  color: #183a56;
}

.agencies-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.agency-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.agency-icon {
  width: 40px;
  height: 40px;
  margin-right: 16px;
  flex-shrink: 0;
}

.agency-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.agency-info {
  flex: 1;
}

.agency-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: bold;
  color: #183a56;
}

.agency-details {
  margin-bottom: 12px;
}

.agency-distance {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #c4151c;
}

.agency-address,
.agency-contact {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #666;
}

.agency-actions {
  display: flex;
  justify-content: flex-end;
}

@media (min-width: 768px) {
  .locator-container {
    flex-direction: row;
  }

  .map-section {
    flex: 2;
  }

  .agencies-section {
    flex: 1;
    max-width: 400px;
    max-height: none;
    border-top: none;
    border-left: 1px solid #e0e0e0;
  }
}

@media (max-width: 767px) {
  .map-section {
    min-height: 300px;
  }

  .agencies-section {
    max-height: 250px;
  }
}

.autocomplete-wrapper {
  top: 30px;
  width: calc(100% - 60px);
  margin: 0 auto;
  z-index: 1;
  background: #fff;
  padding: 0;
  left: 10px;
}

.autocomplete-input {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  color: #333;
  border: 2px solid #d9e3e5;
  background-color: #fff;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.autocomplete-input:focus {
  box-shadow: 0 0 0 3px rgba(0, 112, 210, 0.2);
}

.autocomplete-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  z-index: 99;
  background-color: #fff;
  border: 1px solid #ccc;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  max-height: 220px;
  overflow-y: auto;
  list-style: none;
  padding: 0;
}

.autocomplete-item {
  position: relative;
  padding-left: 36px;
  display: flex;
  flex-direction: row;
  align-items: baseline;
  gap: 4px;
  min-height: 44px;
  color: var( --main_color);
}

.autocomplete-item:before {
  content: '';
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="%235B5B5B"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
}

.autocomplete-item:hover {
  background-color: #f0f0f0;
}

.tpd_wrapper_container_list_agencies {
  overflow-y: auto;
  padding: 20px;
  top: 0;
  left: 0;
  position: relative;
}

.tpd_listAgencies {
  list-style: none;
  margin: 0;
  padding: 0;
}

.tpd_item_agency {
  padding: 20px;
  background: white;
  margin-bottom: 20px;
  border: 1px solid grey;
  margin-right: 7px;
}

.tpd_icons {
  margin-right: 12px;
}

.tpd_icon {
  display: block;
}

.tpd_agency_name {
  font-weight: 600;
  flex: 1;
}

.tpd_agency_wrapper {
  display: flex;
  justify-content: space-between;
  width: 300px;
}

.tpd_agency_left {
  display: flex;
  flex-direction: column;
}

.tpd_bold span {
  font-weight: 700;
}

.tpd_agency_address {
  font-size: 0.85rem;
  color: #666;
}

.tpd_align_center {
  display: flex;
  align-items: center;
}

.tpd_agency_btn {
  border-radius: 0;
  width: 153px;
  height: 47px;
  margin-top: 16px;
  margin-bottom: 10px;
  border: 1px solid #979797;
  border-bottom: 5px solid #c4151c;
  font-size: 16px;
  color: #0f3250;
  font-family: Unipol Bold;
  background: border-box;
}