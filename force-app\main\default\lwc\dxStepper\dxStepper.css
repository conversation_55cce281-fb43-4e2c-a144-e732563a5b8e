.empty-bar {
  background-color: var(--border-card-disabled);
  height: 2px;
  margin-top: 16px;
  margin-bottom: 16px;
}

.empty-bar-pu {
  background-color: var(--light-blue-color);
  height: 4px;
}

.full-bar {
  background-color: var(--dark-light_blue);
  height: 7px;
  margin-top: -3px;
}

.full-bar-pu {
  background-color: var(--medium-light-blue);
  height: 4px;
}

.mrg-stepper {
  margin-bottom: 32px;
}

.StepperContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  background-color: white;
  padding: 24px calc(100% * (4 / 24)) 39px;
  border-bottom: 1px solid var(--ivory);
  margin-bottom: 20px;
}

/*  Single step */
.StepperContainer .StepperStep {
  display: block;
  width: auto;
  height: auto;
  flex-shrink: 0;
  position: relative;
}

/* Circle of step */
.StepperContainer .StepperStep .StepperNumber {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px; 
  border-radius: 50%;
  margin: 0 12px;
  color: #afa4a4;;
  font-family: var(---font-family-default);
  font-weight: 500;
  font-size: 14px;
}

.StepperNumber {
  background-color: rgb(236 231 231 / 50%);
}

.StepperNumber.checked { 
  background-color: #e2f0fc;
}

/* State completed */
.StepperContainer .StepperStep .StepperNumber.checked::after {
  content: "";
  display: block;
  aspect-ratio: 5 / 3;
  width: 30%;
  border-left: 1px solid var(--blue-primary);
  border-bottom: 1px solid var(--blue-primary);
  transform: rotateZ(-45deg);
}

/* Name of step */
.StepperContainer .StepperStep .StepName {
  position: absolute;
  top: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
}

.StepName.TEXT-APP-GRL13-WEB-GRL14-GRL14-GRL14 {
  color: rgb(181, 177, 177);
  font-family: var(--font-family-medium), sans-serif;
}

.StepName.TEXT-APP-BDL13-WEB-BDL14-BDL14-BDL14 {
  color: #082e52c7;; 
  font-family: var(--font-family-default), sans-serif;
}

/* Per lo step corrente, per cui il testo deve apparire in blu acceso */
.StepName.TEXT-APP-BDM13-WEB-BDM14-BDM14-BDM14  {
  color: #23426c;; 
  font-family: var(--font-family-bold), sans-serif;
  font-weight: bold;
}

/* Space with step */
.StepperContainer .StepperSpace {
  display: block;
  flex-grow: 1;
  height: 1px;
  background-color: var(--border-card-disabled);
}

.stepper-cc{
  padding: 20px;
  text-align: center;
  font-weight: bold;
  font-size: 25px;
}

/* Responsive */
@media (max-width: 767px) {
  .mrg-stepper {
    margin-bottom: 32px;
  }

  .StepperContainer {
    padding: 12px calc(100% * (2 / 24)) 31px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .mrg-stepper {
    margin-bottom: 50px;
  }

  .StepperContainer {
    padding: 24px calc(100% * (4 / 24)) 39px;
  }

  .empty-bar-pu,
  .full-bar-pu {
    height: 12px;
  }
}

@media (min-width: 1025px) {
  .mrg-stepper {
    margin-bottom: 75px;
  }

  .StepperContainer {
    padding: 24px calc(100% * (6 / 24)) 43px;
  }
}

