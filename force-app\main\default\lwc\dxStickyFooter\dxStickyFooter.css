
.footer-pu-container {
  position: fixed;
  bottom: 33px;
  left: 0;
  width: 100%;
  z-index: 998;
  border-bottom: 2px solid white;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 2px solid #cccccc;
  padding: 16px;
  height: auto;
  gap: 16px;
}

.separator {
  border-left: 1px solid var(--blue-primary);
  opacity: 0.5;
  height: 50px;
}

.d-flex {
  display: flex;
  column-gap: 16px;
}

@media (min-width: 768px) {
  .d-flex {
    column-gap: 40px;
  }
}

.visible-desktop {
  display: none;
}

@media (min-width: 1024px) {
  .visible-desktop {
    display: block;
  }
}

.visible-tablet {
  display: none;
}

@media (min-width: 768px) {
  .visible-tablet {
    display: block;
  }

  .visible-mobile {
    display: none;
  }
}

