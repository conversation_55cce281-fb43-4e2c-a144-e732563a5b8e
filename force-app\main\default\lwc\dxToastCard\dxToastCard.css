.ToastCardContainer {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: stretch;
  position: relative;
  background-color: white;
  width: 100%;
  top: 0;
  left: 0;
}

@media only screen and (max-width: 767px) {
  .ToastCardContainer {
      position: fixed;
      z-index: 10;
  }
}

@media only screen and (min-width: 768px) {
  .ToastCardContainer {
      position: relative;
      z-index: 0;
  }
}

.ToastCardContainer > .toastType {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 40px 18px;
  width: auto;
  min-height: auto;
  background-color: lightgray;
}

.ToastCardContainer > .toastType.warning {
  background-color: #ffa726;
}

.ToastCardContainer > .toastType.warning::before {
  content: "";
  display: block;
  width: 20px;
  aspect-ratio: 1 / 1;
  background-image: url("https://evo-dev.unipolsai.it/NextAssets/interprete-pu/WhiteAlertIcon.svg");
  background-repeat: no-repeat;
  background-size: contain;
}

.ToastCardContainer > .toastContent {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 35px 10px;
  flex-grow: 1;
  font-family: var(--font-family-default), sans-serif;
  font-size: 18px;
  font-weight: 500;
  color: var(--main_color, #193a56);
  border: 1px solid var(--border-card-disabled, #ccc);
  gap: 8px;
}

@media only screen and (max-width: 767px) {
  .ToastCardContainer > .toastContent {
      font-size: 13px;
  }
}

.ToastCardContainer > .toastContent > .xIcon {
  display: block;
  width: 20px;
  aspect-ratio: 1/1;
  position: relative;
  cursor: pointer;
}

.ToastCardContainer > .toastContent > .xIcon::before,
.ToastCardContainer > .toastContent > .xIcon::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 100%;
  height: 5%;
  background-color: var(--main_color, #193a56);
}

.ToastCardContainer > .toastContent > .xIcon::before {
  transform: translateY(-50%) rotateZ(45deg);
}

.ToastCardContainer > .toastContent > .xIcon::after {
  transform: translateY(-50%) rotateZ(-45deg);
}