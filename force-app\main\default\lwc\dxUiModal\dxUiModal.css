.unipol-modal-content-container{
  position: fixed;
  display: block;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
}

.unipol-modal-content {
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
  margin: auto;
  width: calc(100% - 32px);
  max-height: 90vh;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  box-shadow: 0 0 50vmax #00000080;
}

@media (min-width: 768px) and (max-width: 1281px) {
  .unipol-modal-content {
    max-width: 640px;
  }
}

@media (min-width: 1281px) {
  .unipol-modal-content {
    max-width: 840px;
  }
}