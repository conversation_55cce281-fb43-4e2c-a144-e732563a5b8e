.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 0.5rem 1rem;
}
.label {
  color: var(--lwc-colorTextLabel);
  font-size: 0.75rem;
}
.value {
  font-weight: 600;
}
.step-header {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}
.step-header.error {
  border-left: 4px solid var(--lwc-colorBorderError, #c23934);
  padding-left: 0.5rem;
}
.pill {
  background: var(--lwc-colorBackground, #f3f3f3);
  border-radius: 0.75rem;
  padding: 0.125rem 0.5rem;
  font-size: 0.7rem;
}
pre {
  background: #f8f8f8;
  border-radius: 4px;
  padding: 8px;
  white-space: pre-wrap;
  word-break: break-word;
}
