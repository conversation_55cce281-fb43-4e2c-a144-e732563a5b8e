.main-container {
    /*
    background-color: white;
    border: 1px solid #C9C9C9;
    padding: 16px;
    */
    border-radius: 6px;
}

.table-container {
    border: 1px solid #C9C9C9;
}

.header-container {
    background-color: #F3F3F3;
    padding: 6px;
    text-transform: bold;
    margin-top: 8px;
    border: 1px solid #C9C9C9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-container b {
    margin-top: 10px;
}

.view-all-container {
    background-color: white;
    display: flex;
    justify-content: center;
    padding: 4px;
    text-align: center;
    margin: auto;
}

lightning-icon {
    margin-left: 4px;
    margin-right: 10px;
    margin-top: 4px;
}

.input-container{
    width: 47%;
}

.footer-container{
    margin-top: 10px;
}

.annulla-button{
    margin-right: 5px;
}

.flow-container{
    /*display: none;*/
}

.att-tab {
    cursor: pointer;
    font-size: 16px;
    min-height: 24px;
    /*margin-bottom: 4px;*/
    /*margin-left: 10px;*/
    justify-content: center;
    text-align: center;
}