import { LightningElement, wire, api, track } from 'lwc';
import { NavigationMixin, CurrentPageReference } from 'lightning/navigation';
import { IsConsoleNavigation, openTab, getFocusedTabInfo, setTabIcon} from 'lightning/platformWorkspaceApi';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import Id from '@salesforce/user/Id';
import getRelatedOpportunitiesCC from '@salesforce/apex/OpportunityRelatedListController.getRelatedOpportunitiesCC';
import getAccountInfo from '@salesforce/apex/OpportunityRelatedListController.getAccountInfo';
import getUserInfo from '@salesforce/apex/OpportunityRelatedListController.getUserInfo';
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';

export default class opportunityRelatedListAll extends NavigationMixin(LightningElement) {

    @wire(CurrentPageReference) currentPageRef;
    @wire(IsConsoleNavigation) IsConsoleNavigation;

    @api recordId;
    @api caseId;
    userId = Id;
    account;
    userInfo = {};
    idAzienda = '';

    isViewAll = true; 
    isViewMore = true; 
    isModalOpen = false;
    isFlowVisible = false;

    recordsToShow = 20;

    showTable = false;
    trattativeTitle = 'Tutte le Trattative'; // Titolo statico
    trattativeNumber = 0;
    noRecords = true;

    get columns() {
        const viewAllColumns = [
            { label: 'Data Scadenza', fieldName: 'DueDateFormula__c', type: 'date-local' },
            { label: 'Temperatura', fieldName: 'TemperatureFormula__c',type: 'richText'},
            { label: 'Data creazione', fieldName: 'CreatedDate', type: 'date-local' },
            { label: 'CIP', fieldName: 'CIP__c' },
            { label: 'Nome Trattativa', fieldName: 'nameUrl', type: 'url', typeAttributes: { label: { fieldName: 'Name' }, target: '_self' } },
            { label: 'Premio', fieldName: 'Amount', type: 'currency', typeAttributes: { currencyCode: 'EUR' } },
            { label: 'Stato', fieldName: 'StageName'},
            { label: 'Canale Origine', fieldName: 'Channel__c' },
            { label: 'Prodotto', fieldName: 'Product__c' },
            { label: 'Tipologia soggetto Unipol', fieldName: 'Tipologia_soggetto_Unipol__c'},
            { label: 'Soggetto', fieldName: 'AccountHyperlink__c', type: 'richText'},
            { label: 'Step digitale', fieldName: 'JourneyStep__c'},
            { label: 'Call Me Back', fieldName: 'HasCallMeBackFormula__c', type: 'richText'},
            { label: 'Ambiti di Protezione', fieldName: 'AreasImage', type: 'richText' },
            { label: 'Punto Vendita', fieldName: 'SalespointName__c'}
        ]
        return viewAllColumns;
    }

    records;

    connectedCallback(){
        console.log('pageRef: ' + JSON.stringify(this.currentPageRef));
        console.log('recordID: ' + this.recordId);

        if (this.recordId === undefined && this.currentPageRef?.state?.c__recordId !== undefined) {
            this.recordId = this.currentPageRef.state.c__recordId;
            this.caseId = this.currentPageRef.state.c__caseId;
        }

        this.getAccountInfo();
        this.getUserInfo();
        this.getOpportunities();
    }

    getOpportunities(){
        console.log('accountId: ' + this.recordId);
        getRelatedOpportunitiesCC({accountId: this.recordId, caseId : this.caseId})
        .then(result=>{
            this.records = result;
            this.trattativeNumber = this.records.length? this.records.length : 0;
            console.log('records.length: ' + this.records.length);
            console.log('records pre ' + JSON.stringify(this.records));

            if(this.records.length > 0){
                if(this.records.length <= this.recordsToShow){
                    this.isViewMore = false;    
                } else {
                    this.isViewMore = true;
                }

                if(this.records.length > this.recordsToShow){
                    this.records = this.records.slice(0, this.recordsToShow);
                }
                
                this.records = this.records.map(item =>
                    ({...item,
                        AreasImage: item.AreasOfNeedFormula__c? '<div>' + item.AreasOfNeedFormula__c + '</div>' : '',
                        nameUrl: '/' + item.Id,
                        userUrl: item.AssignedTo__c? '/' + item.AssignedTo__c : '',
                        userName: item.AssignedTo__r?.Name? item.AssignedTo__r.Name : '',
                    })
                )
                
                this.showTable = true;
                
            } else {

            }
        
        }).catch(error=>{
            console.log(JSON.stringify(error));
        });
    }

    getAccountInfo(){
        getAccountInfo({accountId: this.recordId}).then(result=>{
            this.account = result;
        })
    }

    getUserInfo(){
        getUserInfo({userId: this.userId})
            .then(result=>{
            this.userInfo = result;

            if(this.userInfo.IdAzienda__c !== undefined){
                this.idAzienda = this.userInfo.IdAzienda__c;
            }
        })
    }
}