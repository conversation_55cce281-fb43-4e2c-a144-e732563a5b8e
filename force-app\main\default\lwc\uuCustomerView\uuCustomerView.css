.s-section { margin: 0.5rem 1rem 1rem; }
.s-card {
  background: var(--lwc-colorBackgroundAlt, #fff);
  border: 1px solid var(--lwc-colorBorder, #d8dde6);
  border-radius: 0.25rem;
  padding: 0.75rem 1rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}
.s-controls {
  display: flex;
  gap: 0.5rem;
  align-items: end;
}
.s-cta {
  --slds-c-button-brand-color-background: var(--lwc-colorBrand, #0176d3);
}
.s-spinner {
  padding: 0.5rem 1rem;
}
.s-title {
  margin: 0 0 0.5rem;
  font-weight: 700;
}
.s-subtitle {
  margin: 0.5rem 0 0.25rem;
  font-weight: 600;
}
.s-subcard {
  background: var(--lwc-colorBackgroundAlt, #fafbfc);
  border: 1px solid var(--lwc-colorBorder, #e5e7eb);
  border-radius: 0.25rem;
  padding: 0.75rem;
}
.s-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.5rem 1rem;
}
.s-item .s-key { color: var(--lwc-colorTextWeak, #444); margin-right: 0.25rem; }
.s-item .s-val { font-weight: 600; }

.kv { display: grid; gap: 0.25rem 0.75rem; }
.kv-row { display: grid; grid-template-columns: 180px 1fr; align-items: center; }
.kv-key { color: var(--lwc-colorTextWeak, #666); font-weight: 600; }
.kv-val { word-break: break-word; }
.mono { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", monospace; }
.divider { border-top: 1px solid var(--lwc-colorBorder, #e5e7eb); margin: 0.5rem 0; }

.badge { display: inline-block; padding: 0.1rem 0.4rem; border-radius: 999px; background: var(--lwc-colorBackgroundAlt, #f3f4f6); border: 1px solid var(--lwc-colorBorder, #e5e7eb); font-size: 0.75rem; }
.badge-info { background: #eef2ff; border-color: #c7d2fe; color: #3730a3; }
.badge-success { background: #ecfdf5; border-color: #a7f3d0; color: #065f46; }

.s-metric { background: var(--lwc-colorBackgroundAlt, #f3f3f3); padding: 0.5rem; border-radius: 0.25rem; text-align: center; }
.s-metric-num { font-size: 1.25rem; font-weight: 700; display: block; }
.s-metric-label { color: var(--lwc-colorTextWeak, #666); }

.s-list { list-style: none; padding: 0; margin: 0; }
.s-list-item { padding: 0.25rem 0; border-bottom: 1px solid var(--lwc-colorBorder, #ddd); }

.s-pre { max-height: 300px; overflow: auto; background: #f9fafb; padding: 0.5rem; border: 1px solid #eee; border-radius: 4px; }

.s-error { color: var(--lwc-colorTextError, #c23934); padding: 0.5rem 1rem; }

/* simple table-like layout */
.s-table { display: grid; grid-auto-rows: minmax(32px, auto); row-gap: 2px; }
.s-thead { display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; font-weight: 700; color: var(--lwc-colorTextWeak, #666); border-bottom: 1px solid var(--lwc-colorBorder, #d8dde6); padding-bottom: 0.25rem; margin-bottom: 0.25rem; }
.s-tr { display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; column-gap: 0.5rem; padding: 0.25rem 0; border-bottom: 1px dashed var(--lwc-colorBorder, #eee); }
.s-td { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.s-empty { color: var(--lwc-colorTextWeak, #666); font-style: italic; }

/* spacers */
.s-m-top { margin-top: 0.75rem; }
.s-m-top-xsmall { margin-top: 0.25rem; }

/* form layout */
.form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 0.75rem 1rem; }

@media (max-width: 520px) {
  .kv-row { grid-template-columns: 1fr; }
}
