.qs { display: grid; gap: 8px; }
.qs-list { list-style: none; padding: 0; margin: 0; max-height: 50vh; overflow: auto; }
.qs-item { padding: 8px 10px; display: flex; gap: 8px; align-items: baseline; cursor: pointer; border-radius: 6px; }
.qs-item:hover, .qs-item.active { background: var(--lwc-colorBackgroundAlt, #f3f3f3); }
.qs-label { font-weight: 600; }
.qs-name { color: #666; font-size: 0.85em; }
.qs-empty { color: #666; padding: 12px; }
