-------------------------------------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------------------------------------
-------------------------------------------------------------------------------------------------------------
Manual POST-DEPLOY procedure related to Salesforce Lightning Knowledge configuration and verification after deployment of metadata.
Goal: ensure Knowledge features, permissions, data categories, record types, and article usability are correctly functioning.
-------------------------------------------------------------------------------------------------------------
--------------------------- MANUAL POST-DEPLOY PROCEDURE STEPS --------------------------------------------
-------------------------------------------------------------------------------------------------------------
Prerequisites / Notes:
- Perform only AFTER the pre-deploy procedure and metadata deployment have completed successfully.
- System Administrator access required.

1. Verify Lightning Knowledge Enablement
   a. Navigate to Setup > Knowledge Settings.

2. Permission Sets / Profiles
   a. Assign Permission Set License Assignments (Service Lightning Knowledge Manager)
   b. Assign Knowledge-related Permission Sets (Lightning Knowledge Manager)

Completion Criteria:
- Permissions validated (least privilege confirmed).

-------------------------------------------------------------------------------------------------------------
--------------------------------- END OF POST-DEPLOY PROCEDURE ---------------------------------------------
-------------------------------------------------------------------------------------------------------------
