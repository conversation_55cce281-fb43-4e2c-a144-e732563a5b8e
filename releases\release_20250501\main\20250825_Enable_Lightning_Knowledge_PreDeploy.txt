-------------------------------------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------------------------------------
-------------------------------------------------------------------------------------------------------------
Manual PRE-DEPLOY procedure to (1) enable the "Knowledge User" checkbox on the deployment user and (2) enable Salesforce Lightning Knowledge in the target org before running the metadata deployment.
This is mandatory for correct provisioning of Knowledge metadata (articles, data categories, layouts, permissions).
-------------------------------------------------------------------------------------------------------------
--------------------------- MANUAL PRE-DEPLOY PROCEDURE STEPS ----------------------------------------------
-------------------------------------------------------------------------------------------------------------
Prerequisites / Notes:
- System Administrator access is required.
- Identify the technical deployment user (username): __________________________
- Execute all steps in the TARGET environment (not in sandbox unless that is the deploy target).

1. Log in to the target org as System Administrator.
2. Go to Setup (gear icon > Setup).
3. Navigate to Users > Users (or use Quick Find: "Users").
4. Open the deployment user record.
5. Click Edit.
6. Tick the checkbox "Knowledge User".
7. Click Save.
8. In Quick Find, search for "Knowledge Settings" and open it (Path: Setup > Feature Settings > Service > Knowledge Settings OR Feature Settings > Knowledge > Knowledge Settings depending on UI grouping).
9. If an Edit button is shown, click it.
10. Tick the acknowledgement checkbox: "Yes, I understand the impact of enabling Salesforce Knowledge".
11. Click "Enable Salesforce Lightning Knowledge".
12. Save (if prompted) and wait until the page refreshes.
13. Validation:
    - The enable button is no longer visible.
    - Additional Knowledge configuration sections are displayed (Languages, Data Category Settings, etc.).

Proceed to deployment only after successful completion of the above validation.
-------------------------------------------------------------------------------------------------------------
--------------------------------- END OF PRE-DEPLOY PROCEDURE ----------------------------------------------
-------------------------------------------------------------------------------------------------------------
